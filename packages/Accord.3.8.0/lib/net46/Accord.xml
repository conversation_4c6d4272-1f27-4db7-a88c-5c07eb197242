<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Accord</name>
    </assembly>
    <members>
        <member name="T:Accord.MessageTransferHandler">
             <summary>
             A delegate which is used by events notifying abount sent/received message.
             </summary>
             
             <param name="sender">Event sender.</param>
             <param name="eventArgs">Event arguments containing details about the transferred message.</param>
            
        </member>
        <member name="T:Accord.DoublePoint">
            <summary>
            Structure for representing a pair of coordinates of double type.
            </summary>
            
            <remarks><para>The structure is used to store a pair of floating point
            coordinates with double precision.</para>
            
            <para>Sample usage:</para>
            <code>
            // assigning coordinates in the constructor
            DoublePoint p1 = new DoublePoint( 10, 20 );
            // creating a point and assigning coordinates later
            DoublePoint p2;
            p2.X = 30;
            p2.Y = 40;
            // calculating distance between two points
            double distance = p1.DistanceTo( p2 );
            </code>
            </remarks>
            
        </member>
        <member name="F:Accord.DoublePoint.X">
            <summary> 
            X coordinate.
            </summary> 
            
        </member>
        <member name="F:Accord.DoublePoint.Y">
            <summary> 
            Y coordinate.
            </summary> 
            
        </member>
        <member name="M:Accord.DoublePoint.#ctor(System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DoublePoint"/> structure.
            </summary>
            
            <param name="x">X axis coordinate.</param>
            <param name="y">Y axis coordinate.</param>
            
        </member>
        <member name="M:Accord.DoublePoint.DistanceTo(Accord.DoublePoint)">
            <summary>
            Calculate Euclidean distance between two points.
            </summary>
            
            <param name="anotherPoint">Point to calculate distance to.</param>
            
            <returns>Returns Euclidean distance between this point and
            <paramref name="anotherPoint"/> points.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.SquaredDistanceTo(Accord.DoublePoint)">
            <summary>
            Calculate squared Euclidean distance between two points.
            </summary>
            
            <param name="anotherPoint">Point to calculate distance to.</param>
            
            <returns>Returns squared Euclidean distance between this point and
            <paramref name="anotherPoint"/> points.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Addition(Accord.DoublePoint,Accord.DoublePoint)">
            <summary>
            Addition operator - adds values of two points.
            </summary>
            
            <param name="point1">First point for addition.</param>
            <param name="point2">Second point for addition.</param>
            
            <returns>Returns new point which coordinates equal to sum of corresponding
            coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.Add(Accord.DoublePoint,Accord.DoublePoint)">
            <summary>
            Addition operator - adds values of two points.
            </summary>
            
            <param name="point1">First point for addition.</param>
            <param name="point2">Second point for addition.</param>
            
            <returns>Returns new point which coordinates equal to sum of corresponding
            coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Subtraction(Accord.DoublePoint,Accord.DoublePoint)">
             <summary>
             Subtraction operator - subtracts values of two points.
             </summary>
             
             <param name="point1">Point to subtract from.</param>
             <param name="point2">Point to subtract.</param>
             
             <returns>Returns new point which coordinates equal to difference of corresponding
             coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.Subtract(Accord.DoublePoint,Accord.DoublePoint)">
             <summary>
             Subtraction operator - subtracts values of two points.
             </summary>
             
             <param name="point1">Point to subtract from.</param>
             <param name="point2">Point to subtract.</param>
             
             <returns>Returns new point which coordinates equal to difference of corresponding
             coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Addition(Accord.DoublePoint,System.Double)">
            <summary>
            Addition operator - adds scalar to the specified point.
            </summary>
            
            <param name="point">Point to increase coordinates of.</param>
            <param name="valueToAdd">Value to add to coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point increased by specified value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.Add(Accord.DoublePoint,System.Double)">
            <summary>
            Addition operator - adds scalar to the specified point.
            </summary>
            
            <param name="point">Point to increase coordinates of.</param>
            <param name="valueToAdd">Value to add to coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point increased by specified value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Subtraction(Accord.DoublePoint,System.Double)">
            <summary>
            Subtraction operator - subtracts scalar from the specified point.
            </summary>
            
            <param name="point">Point to decrease coordinates of.</param>
            <param name="valueToSubtract">Value to subtract from coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point decreased by specified value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.Subtract(Accord.DoublePoint,System.Double)">
            <summary>
            Subtraction operator - subtracts scalar from the specified point.
            </summary>
            
            <param name="point">Point to decrease coordinates of.</param>
            <param name="valueToSubtract">Value to subtract from coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point decreased by specified value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Multiply(Accord.DoublePoint,System.Double)">
             <summary>
             Multiplication operator - multiplies coordinates of the specified point by scalar value.
             </summary>
             
             <param name="point">Point to multiply coordinates of.</param>
             <param name="factor">Multiplication factor.</param>
             
             <returns>Returns new point which coordinates equal to coordinates of
             the specified point multiplied by specified value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.Multiply(Accord.DoublePoint,System.Double)">
             <summary>
             Multiplication operator - multiplies coordinates of the specified point by scalar value.
             </summary>
             
             <param name="point">Point to multiply coordinates of.</param>
             <param name="factor">Multiplication factor.</param>
             
             <returns>Returns new point which coordinates equal to coordinates of
             the specified point multiplied by specified value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Division(Accord.DoublePoint,System.Double)">
            <summary>
            Division operator - divides coordinates of the specified point by scalar value.
            </summary>
            
            <param name="point">Point to divide coordinates of.</param>
            <param name="factor">Division factor.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point divided by specified value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.Divide(Accord.DoublePoint,System.Double)">
            <summary>
            Division operator - divides coordinates of the specified point by scalar value.
            </summary>
            
            <param name="point">Point to divide coordinates of.</param>
            <param name="factor">Division factor.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point divided by specified value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Equality(Accord.DoublePoint,Accord.DoublePoint)">
             <summary>
             Equality operator - checks if two points have equal coordinates.
             </summary>
             
             <param name="point1">First point to check.</param>
             <param name="point2">Second point to check.</param>
             
             <returns>Returns <see langword="true"/> if coordinates of specified
             points are equal.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Inequality(Accord.DoublePoint,Accord.DoublePoint)">
             <summary>
             Inequality operator - checks if two points have different coordinates.
             </summary>
             
             <param name="point1">First point to check.</param>
             <param name="point2">Second point to check.</param>
             
             <returns>Returns <see langword="true"/> if coordinates of specified
             points are not equal.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.Equals(System.Object)">
            <summary>
            Check if this instance of <see cref="T:Accord.DoublePoint"/> equal to the specified one.
            </summary>
            
            <param name="obj">Another point to check equalty to.</param>
            
            <returns>Return <see langword="true"/> if objects are equal.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.GetHashCode">
            <summary>
            Get hash code for this instance.
            </summary>
            
            <returns>Returns the hash code for this instance.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Explicit(Accord.DoublePoint)~Accord.IntPoint">
            <summary>
            Explicit conversion to <see cref="T:Accord.IntPoint"/>.
            </summary>
            
            <param name="point">Double precision point to convert to integer point.</param>
            
            <returns>Returns new integer point which coordinates are explicitly converted
            to integers from coordinates of the specified double precision point by
            casting double values to integers value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.op_Explicit(Accord.DoublePoint)~Accord.Point">
            <summary>
            Explicit conversion to <see cref="T:Accord.Point"/>.
            </summary>
            
            <param name="point">Double precision point to convert to single precision point.</param>
            
            <returns>Returns new single precision point which coordinates are explicitly converted
            to floats from coordinates of the specified double precision point by
            casting double values to float value.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.Round">
            <summary>
            Rounds the double precision point.
            </summary>
            
            <returns>Returns new integer point, which coordinates equal to whole numbers
            nearest to the corresponding coordinates of the double precision point.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.ToString">
             <summary>
             Get string representation of the class.
             </summary>
             
             <returns>Returns string, which contains values of the point in readable form.</returns>
            
        </member>
        <member name="M:Accord.DoublePoint.EuclideanNorm">
            <summary>
            Calculate Euclidean norm of the vector comprised of the point's 
            coordinates - distance from (0, 0) in other words.
            </summary>
            
            <returns>Returns point's distance from (0, 0) point.</returns>
            
        </member>
        <member name="T:Accord.FormatDecoderAttribute">
            <summary>
              Specifies that a class can be used to decode a particular file type.
            </summary>
            
        </member>
        <member name="P:Accord.FormatDecoderAttribute.Extension">
            <summary>
              Gets or sets the file extension that this format decoder applies to (i.e. "wav").
            </summary>
            
        </member>
        <member name="M:Accord.FormatDecoderAttribute.#ctor(System.String)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.FormatDecoderAttribute"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.FormatDecoderAttribute.PopulateDictionaryWithDecodersFromAllAssemblies``1(System.Collections.Generic.Dictionary{System.String,System.Type},System.String)">
            <summary>
              Populates the dictionary with available decoders of a particular category by 
              inspecting types from all referenced assemblies. Note: calling this method
              will force all referenced assemblies to be loaded into the current AppDomain.
            </summary>
            
            <typeparam name="T">The base type for the decoders. This should be an interface such as IImageDecoder or IAudioDecoder.</typeparam>
            
            <param name="dictionary">The dictionary where the found decoders will be stored.</param>
            <param name="extension">The extension we are interested in.</param>
            
        </member>
        <member name="T:Accord.SourceTypeAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be greater than zero.
            </summary>
            
        </member>
        <member name="P:Accord.SourceTypeAttribute.SourceType">
            <summary>
              Gets or sets the type of the source.
            </summary>
            
            <value>
              The type of the source.
            </value>
            
        </member>
        <member name="M:Accord.SourceTypeAttribute.#ctor(System.Type)">
            <summary>
              Initializes a new instance of the <see cref="P:Accord.SourceTypeAttribute.SourceType"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.SerializationBinderAttribute">
            <summary>
              Specifies a serialization binder to be used whenever a class is 
              being deserialized by the framework. This can be used to ensure 
              binary compatibility when the framework code changes.
            </summary>
            
        </member>
        <member name="P:Accord.SerializationBinderAttribute.Binder">
            <summary>
              The binder to be used for the class marked with this attribute.
            </summary>
            
        </member>
        <member name="M:Accord.SerializationBinderAttribute.#ctor(System.Type)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.SerializationBinderAttribute"/> class.
            </summary>
            
            <param name="binderType">The binder to be used to deserialize objects of this type.</param>
            
        </member>
        <member name="T:Accord.SurrogateSelectorAttribute">
            <summary>
              Specifies a serialization surrogate to be used whenever a class is 
              being deserialized by the framework. This can be used to ensure 
              binary compatibility when the framework code changes.
            </summary>
            
        </member>
        <member name="P:Accord.SurrogateSelectorAttribute.Selector">
            <summary>
              The binder to be used for the class marked with this attribute.
            </summary>
            
        </member>
        <member name="M:Accord.SurrogateSelectorAttribute.#ctor(System.Type)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.SurrogateSelectorAttribute"/> class.
            </summary>
            
            <param name="surrogateSelectorType">The surrogate selector to be used to deserialize objects of this type.</param>
            
        </member>
        <member name="T:Accord.Collections.BinaryNode`1">
            <summary>
              Tree node for binary trees.
            </summary>
            
            <typeparam name="TNode">The class type for the nodes of the tree.</typeparam>
            
        </member>
        <member name="P:Accord.Collections.BinaryNode`1.Left">
            <summary>
              Gets or sets the left subtree of this node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.BinaryNode`1.Right">
            <summary>
              Gets or sets the right subtree of this node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.BinaryNode`1.IsLeaf">
            <summary>
              Gets whether this node is a leaf (has no children).
            </summary>
            
        </member>
        <member name="M:Accord.Collections.BinaryNode`1.Equals(`0)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
            
        </member>
        <member name="P:Accord.Collections.BinaryNode`1.Children">
            <summary>
              Gets or sets the collection of child nodes
              under this node.
            </summary>
            
        </member>
        <member name="T:Accord.Collections.BinaryTree`1">
            <summary>
              Base class for binary trees. This class does not implement a binary search
              tree, but can used to implement one. For binary search trees, please refer
              to <see cref="T:Accord.Collections.RedBlackTree`1"/>, KDTree and VPTree.
            </summary>
            
            <example>
            <para>
              The <see cref="T:Accord.Collections.BinaryTree`1"/> class is a base class for other tree classes
              such as <see cref="T:Accord.Collections.RedBlackTree`1"/>, KDTree and VPTree. For examples on how to
              use those classes, please see their respective documentation pages.</para>
              
            <para>
              If you would like to implement your own binary tree that inherits from this class,
              then you can do so as shown in the following example. First, make sure your custom
              node class inherits from <see cref="T:Accord.Collections.BinaryNode`1"/> and passes itself as the generic 
              argument of <see cref="T:Accord.Collections.BinaryNode`1"/>:</para>
            <code source="Unit Tests\Accord.Tests.Core\BinaryTreeTest.cs" region="doc_ctor_1" />
            
            <para>
              Now, once the tree node has been implemented, we can create a new <see cref="T:Accord.Collections.BinaryTree`1"/>
              and explore the tree in different ways as shown below:</para>
            <code source="Unit Tests\Accord.Tests.Core\BinaryTreeTest.cs" region="doc_ctor_2" />
            </example>
            
            <typeparam name="TNode">The class type for the nodes of the tree.</typeparam>
            
        </member>
        <member name="P:Accord.Collections.BinaryTree`1.Root">
            <summary>
              Gets the root node of this tree.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.BinaryTree`1.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the tree.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator"/> object 
              that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.BinaryTree`1.Traverse(Accord.Collections.BinaryTraversalMethod{`0})">
            <summary>
              Traverse the tree using a <see cref="T:Accord.Collections.TreeTraversal">tree traversal
              method</see>. Can be iterated with a foreach loop.
            </summary>
            
            <param name="method">The tree traversal method. Common methods are
            available in the <see cref="T:Accord.Collections.TreeTraversal"/>static class.</param>
            
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1"/> object which can be used to
            traverse the tree using the chosen traversal method.</returns>
            
        </member>
        <member name="M:Accord.Collections.BinaryTree`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the tree.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="T:Accord.Collections.ITreeNode`1">
            <summary>
              Common interface for tree nodes, such as <see cref="T:Accord.Collections.BinaryTree`1"/> and <see cref="T:Accord.Collections.TreeNode`1"/>.
            </summary>
            
            <typeparam name="TNode">The type of the tree node.</typeparam>
            
            <seealso cref="T:Accord.Collections.BinaryTree`1"/>
            <seealso cref="T:Accord.Collections.TreeNode`1"/>
            <seealso cref="T:Accord.Collections.RedBlackTree`1"/>
            
        </member>
        <member name="P:Accord.Collections.ITreeNode`1.Children">
            <summary>
              Gets or sets the collection of child nodes
              under this node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.ITreeNode`1.IsLeaf">
            <summary>
              Gets whether this node is a leaf (has no children).
            </summary>
            
        </member>
        <member name="T:Accord.Collections.PriorityOrder">
            <summary>
              Priority order for <see cref="T:Accord.Collections.PriorityQueue`1"/>.
            </summary>
            
        </member>
        <member name="F:Accord.Collections.PriorityOrder.Minimum">
            <summary>
              Minimum order. In Minimum Priority Queues, items with smaller
              priority numbers receive higher priority and are the ones that will
              be dequeued first (i.e. similar to NICE number in UNIX systems).
            </summary>
            
        </member>
        <member name="F:Accord.Collections.PriorityOrder.Maximum">
            <summary>
              MAximum order. In Maximum Priority Queues, items with higher
              priority numbers receive higher priority and are the ones that will
              be dequeued first.
            </summary>
            
        </member>
        <member name="T:Accord.Collections.PriorityQueue`1">
            <summary>
              Priority queue.
            </summary>
            
            <typeparam name="T">The values in the queue.</typeparam>
            
            <remarks>
              The code for this class has been based on the original "High-speed Priority Queue"
              project originally developed by Daniel "Blue Raja" Pflughoeft. It was originally
              shared under a free MIT license, as shown below:
              <code>
                 The MIT License (MIT)
                   
                 Copyright (c) 2013 Daniel "BlueRaja" Pflughoeft
                 https://github.com/BlueRaja/High-Speed-Priority-Queue-for-C-Sharp
                   
                 Permission is hereby granted, free of charge, to any person obtaining a copy
                 of this software and associated documentation files (the "Software"), to deal
                 in the Software without restriction, including without limitation the rights
                 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
                 copies of the Software, and to permit persons to whom the Software is
                 furnished to do so, subject to the following conditions:
                 
                 The above copyright notice and this permission notice shall be included in
                 all copies or substantial portions of the Software.
                 
                 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
                 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
                 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
                 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
                 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
                 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
                 THE SOFTWARE.
              </code>
            </remarks>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.#ctor(System.Int32,Accord.Collections.PriorityOrder)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.Collections.PriorityQueue`1"/> class.
            </summary>
            
            <param name="capacity">The initial capacity for the queue.</param>
            <param name="order">The sort order for the queue. If set to <see cref="F:Accord.Collections.PriorityOrder.Minimum"/>, 
              items that have a smaller priority number are the next to be dequeued. If set to 
              <see cref="F:Accord.Collections.PriorityOrder.Maximum"/>, items with larger priority numbers are the ones
              with higher priority and will be the next to be dequeued. Default is <see cref="F:Accord.Collections.PriorityOrder.Minimum"/>.</param>
            
            <exception cref="T:System.InvalidOperationException">New queue size cannot be smaller than 1.</exception>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueue`1.Count">
            <summary>
              Gets the number of nodes in the queue. This is an O(1) operation.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueue`1.Capacity">
            <summary>
              Gets the current capacity of this queue.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueue`1.Order">
            <summary>
              Gets or sets the <see cref="T:Accord.Collections.PriorityOrder">ordering of this priority queue</see>. 
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.Clear">
            <summary>
              Removes every node from the queue. This is an O(1) operation.
            </summary>
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.Contains(Accord.Collections.PriorityQueueNode{`0})">
            <summary>
                Returns whether the given node is in the queue. This is an O(1) operation.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.Enqueue(`0,System.Double)">
            <summary>
              Enqueue a node to the priority queue. Lower values are placed in front.
              Ties are broken by first-in-first-out. This is an O(log n) operation.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.HasHigherPriority(System.Int32,System.Int32)">
            <summary>
              Returns true if 'higher' has higher priority than 'lower', false otherwise. Note that
              calling HasHigherPriority(node, node) (ie. both arguments the same node) will return false.
            </summary>
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.Dequeue">
            <summary>
              Removes the head of the queue (node with minimum priority; ties are broken by order
              of insertion), and returns it. This is an O(log n) operation.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.Resize(System.Int32)">
            <summary>
              Resize the queue so it can accept more nodes.  All currently enqueued nodes are kept.
              Attempting to decrease the queue size to a size too small to hold the existing nodes
              results in undefined behavior. This is an O(n) operation.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueue`1.First">
            <summary>
              Returns the head of the queue, without removing it (use Dequeue() for that).
              If the queue is empty, behavior is undefined. This is an O(1) operation.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueue`1.IsReadOnly">
            <summary>
              Gets a value indicating whether this instance is read only (returns false).
            </summary>
            
            <value>
              Returns <c>false</c>, as instances of this class are not read only.
            </value>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.UpdatePriority(Accord.Collections.PriorityQueueNode{`0}@,System.Double)">
            <summary>
              This method must be called on a node every time its priority changes while it is in the queue.  
              <b>Forgetting to call this method will result in a corrupted queue!</b>. This is an O(log n) operation.
            </summary>
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.Remove(Accord.Collections.PriorityQueueNode{`0})">
            <summary>
              Removes a node from the queue. The node does not need to be the head of the
              queue. This is an O(log n) operation.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the collection.
            </summary>
            
            <returns>
              A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.ToArray(System.Boolean)">
            <summary>
              Returns an array containing the items in this list, 
              optionally in in priority order.
            </summary>
            
            <param name="sorted">Whether to return the items in priority order.</param>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.IsValidQueue">
            <summary>
              Checks to make sure the queue is still in a valid state.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueue`1.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="T:Accord.Collections.PriorityQueueNode`1">
            <summary>
              Represents the node of a priority queue.
            </summary>
            
            <typeparam name="T">The type for the values stored in the queue.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.#ctor(`0,System.Double,System.Int32,System.Int64)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.Collections.PriorityQueueNode`1"/> struct.
            </summary>
            
            <param name="value">The value to store in the node.</param>
            <param name="priority">A double value representing the priority for the node.</param>
            <param name="index">The index of the node in the priority queue.</param>
            <param name="insertionIndex">The original index of the node in the priority queue, at time of first insertion.</param>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueueNode`1.Priority">
            <summary>
              Gets a double-value representing the 
              current priority for the node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueueNode`1.Value">
            <summary>
              Gets or sets the current value associated with this node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueueNode`1.InsertionIndex">
            <summary>
              Gets the original position at which this node was inserted.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueueNode`1.QueueIndex">
            <summary>
              Gets the current position of this node in its containing queue.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.Equals(System.Object)">
            <summary>
              Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
            
            <param name="obj">The <see cref="T:System.Object" /> to compare with this instance.</param>
            
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.GetHashCode">
            <summary>
              Returns a hash code for this instance.
            </summary>
            
            <returns>
              A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
            
        </member>
        <member name="P:Accord.Collections.PriorityQueueNode`1.IsEmpty">
            <summary>
              Gets a value indicating whether this node is empty (does not belong to any queue).
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.Equals(Accord.Collections.PriorityQueueNode{`0})">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.CompareTo(Accord.Collections.PriorityQueueNode{`0})">
            <summary>
            Compares the current object with another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            A value that indicates the relative order of the objects being compared. The return value has the following meanings: Value Meaning Less than zero This object is less than the <paramref name="other" /> parameter.Zero This object is equal to <paramref name="other" />. Greater than zero This object is greater than <paramref name="other" />.
            </returns>
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.op_Equality(Accord.Collections.PriorityQueueNode{`0},Accord.Collections.PriorityQueueNode{`0})">
            <summary>
              Implements the equals operator.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.op_Inequality(Accord.Collections.PriorityQueueNode{`0},Accord.Collections.PriorityQueueNode{`0})">
            <summary>
              Implements the not equals operator.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.op_GreaterThan(Accord.Collections.PriorityQueueNode{`0},Accord.Collections.PriorityQueueNode{`0})">
            <summary>
              Implements the greater than operator.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.op_LessThan(Accord.Collections.PriorityQueueNode{`0},Accord.Collections.PriorityQueueNode{`0})">
            <summary>
              Implements the less than operator.
            </summary>
            
        </member>
        <member name="F:Accord.Collections.PriorityQueueNode`1.Empty">
            <summary>
              Gets an instance representing an empty node.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.PriorityQueueNode`1.ToString">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="T:Accord.Collections.OrderedDictionary`2">
            <summary>
              Ordered dictionary.
            </summary>
            
            <remarks>
              This class provides a ordered dictionary implementation for C#/.NET. Unlike the rest
              of the framework, this class is available under a MIT license, so please feel free to
              re-use its source code in your own projects.
            </remarks>
            
            <typeparam name="TKey">The types of the keys in the dictionary.</typeparam>
            <typeparam name="TValue">The type of values in the dictionary.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Collections.OrderedDictionary`2"/> class.
            </summary>
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Collections.OrderedDictionary`2"/> class.
            </summary>
            
            <param name="capacity">The initial number of elements that the <see cref="T:Accord.Collections.OrderedDictionary`2"/> can contain.</param>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Collections.OrderedDictionary`2"/> class.
            </summary>
            
            <param name="comparer">The IEqualityComparer implementation to use when comparing keys, or null to use 
                the default EqualityComparer for the type of the key.</param>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Collections.OrderedDictionary`2"/> class.
            </summary>
            
            <param name="capacity">The initial number of elements that the <see cref="T:Accord.Collections.OrderedDictionary`2"/> can contain.</param>
            <param name="comparer">The IEqualityComparer implementation to use when comparing keys, or null to use 
                the default EqualityComparer for the type of the key.</param>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Collections.OrderedDictionary`2"/> class.
            </summary>
            
            <param name="dictionary">The System.Collections.Generic.IDictionary`2 whose elements are copied to the
                new <see cref="T:Accord.Collections.OrderedDictionary`2"/>.</param>
            <param name="comparer">The IEqualityComparer implementation to use when comparing keys, or null to use 
                the default EqualityComparer for the type of the key.</param>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.GetKeyByIndex(System.Int32)">
            <summary>
              Gets the <typeparam ref="TValue"/> at the specified index.
            </summary>
            
            <param name="index">The index.</param>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.GetValueByIndex(System.Int32)">
            <summary>
              Gets the <typeparam ref="TValue"/> at the specified index.
            </summary>
            
            <param name="index">The index.</param>
            
        </member>
        <member name="P:Accord.Collections.OrderedDictionary`2.Item(`0)">
            <summary>
              Gets or sets the <typeparam ref="TValue"/> with the specified key.
            </summary>
            
            <param name="key">The key.</param>
            
        </member>
        <member name="P:Accord.Collections.OrderedDictionary`2.Keys">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
            <value>The keys.</value>
            
        </member>
        <member name="P:Accord.Collections.OrderedDictionary`2.Values">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.OrderedDictionary`2.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.OrderedDictionary`2.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.
            </summary>
            
            <value><c>true</c> if this instance is read only; otherwise, <c>false</c>.</value>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.Add(`0,`1)">
            <summary>
            Adds an element with the provided key and value to the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
            <param name="key">The object to use as the key of the element to add.</param>
            <param name="value">The object to use as the value of the element to add.</param>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.Add(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            
            <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific value.
            </summary>
            
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            
            <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.ContainsKey(`0)">
            <summary>
            Determines whether the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key.
            </summary>
            
            <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</param>
            
            <returns>true if the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the key; otherwise, false.</returns>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.
            </summary>
            
            <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.ICollection`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            
            <returns>An enumerator that can be used to iterate through the collection.</returns>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.Remove(`0)">
            <summary>
            Removes the element with the specified key from the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
            <param name="key">The key of the element to remove.</param>
            
            <returns>true if the element is successfully removed; otherwise, false.  This method also returns false if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            
            <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            
            <returns>true if <paramref name="item" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> is not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.TryGetValue(`0,`1@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
            
            <returns>true if the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
            
        </member>
        <member name="M:Accord.Collections.OrderedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
            
        </member>
        <member name="T:Accord.Collections.Tree`1">
            <summary>
              Base class for tree structures.
            </summary>
            
            <typeparam name="TNode">The class type for the nodes of the tree.</typeparam>
            
        </member>
        <member name="P:Accord.Collections.Tree`1.Root">
            <summary>
              Gets the root node of this tree.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.Tree`1.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the collection.
            </summary>
            
            <returns>
              A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.Tree`1.Traverse(Accord.Collections.TraversalMethod{`0})">
            <summary>
              Traverse the tree using a <see cref="T:Accord.Collections.TreeTraversal">tree traversal
              method</see>. Can be iterated with a foreach loop.
            </summary>
            
            <param name="method">The tree traversal method. Common methods are
            available in the <see cref="T:Accord.Collections.TreeTraversal"/>static class.</param>
            
            <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1"/> object which can be used to
            traverse the tree using the chosen traversal method.</returns>
            
        </member>
        <member name="M:Accord.Collections.Tree`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the tree.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="T:Accord.Collections.TreeNode`1">
            <summary>
              Tree node with arbitrary number of children.
            </summary>
            
            <typeparam name="TNode">The class type for the nodes of the tree.</typeparam>
            
        </member>
        <member name="P:Accord.Collections.TreeNode`1.Parent">
            <summary>
              Gets or sets the parent of this node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TreeNode`1.Index">
            <summary>
              Gets or sets the index of this node in the
              collection of children nodes of its parent.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TreeNode`1.Children">
            <summary>
              Gets or sets the collection of child nodes
              under this node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TreeNode`1.Next">
            <summary>
              Gets the next sibling of this node (the node
              immediately next to it in its parent's collection).
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TreeNode`1.Previous">
            <summary>
              Gets the previous sibling of this node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TreeNode`1.IsLeaf">
            <summary>
              Gets whether this node is a leaf (has no children).
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TreeNode`1.#ctor(System.Int32)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.Collections.TreeNode`1"/> class.
            </summary>
            
            <param name="index">The index of this node in the children collection of its parent node.</param>
            
        </member>
        <member name="T:Accord.Collections.BinaryTraversalMethod`1">
            <summary>
              Tree enumeration method delegate.
            </summary>
            
            <typeparam name="TNode">The class type for the nodes of the tree.</typeparam>
            
            <param name="tree">The binary tree to be traversed.</param>
            
            <returns>An enumerator traversing the tree.</returns>
            
        </member>
        <member name="T:Accord.Collections.TraversalMethod`1">
            <summary>
              Tree enumeration method delegate.
            </summary>
            
            <typeparam name="TNode">The class type for the nodes of the tree.</typeparam>
            
            <returns>An enumerator traversing the tree.</returns>
            
        </member>
        <member name="T:Accord.Collections.TreeTraversal">
            <summary>
              Static class with tree traversal methods.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TreeTraversal.BreadthFirst``1(Accord.Collections.BinaryTree{``0})">
            <summary>
              Breadth-first tree traversal method.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TreeTraversal.PreOrder``1(Accord.Collections.BinaryTree{``0})">
            <summary>
              Pre-order tree traversal method.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TreeTraversal.InOrder``1(Accord.Collections.BinaryTree{``0})">
            <summary>
              In-order tree traversal method.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TreeTraversal.PostOrder``1(Accord.Collections.BinaryTree{``0})">
            <summary>
              Post-order tree traversal method.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TreeTraversal.DepthFirst``1(Accord.Collections.BinaryTree{``0})">
            <summary>
              Depth-first tree traversal method.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TreeTraversal.DepthFirst``1(Accord.Collections.Tree{``0})">
            <summary>
              Depth-first tree traversal method.
            </summary>
            
        </member>
        <member name="T:Accord.Collections.RedBlackTree`2">
            <summary>
              Red-black tree specialized for key-based value retrieval.
            </summary>
            
            <remarks>
              See <see cref="T:Accord.Collections.RedBlackTree`1"/>.
            </remarks>
            
            <typeparam name="TKey">The type of the key.</typeparam>
            <typeparam name="TValue">The type of the value.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`2.#ctor">
            <summary>
              Constructs a new <see cref="T:Accord.Collections.RedBlackTree`1"/> using the default
              <see cref="T:System.Collections.Generic.IComparer`1"/> for the key type <typeparamref name="TKey"/>.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`2.#ctor(System.Collections.Generic.IComparer{System.Collections.Generic.KeyValuePair{`0,`1}})">
            <summary>
              Constructs a new <see cref="T:Accord.Collections.RedBlackTree`1"/> using 
              the provided <see cref="T:System.Collections.Generic.IComparer`1"/> implementation.
            </summary>
            
            <param name="comparer">
              The element comparer to be used to order elements in the tree.</param>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`2.#ctor(System.Boolean)">
            <summary>
              Constructs a new <see cref="T:Accord.Collections.RedBlackTree`1"/> using the default
              <see cref="T:System.Collections.Generic.IComparer`1"/> for the key type <typeparamref name="TKey"/>.
            </summary>
            
            <param name="allowDuplicates">
              Pass <c>true</c> to allow duplicate elements 
              in the tree; <c>false</c> otherwise.</param>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`2.#ctor(System.Collections.Generic.IComparer{System.Collections.Generic.KeyValuePair{`0,`1}},System.Boolean)">
            <summary>
              Constructs a new <see cref="T:Accord.Collections.RedBlackTree`1"/> using 
              the provided <see cref="T:System.Collections.Generic.IComparer`1"/> implementation.
            </summary>
            
            <param name="comparer">
              The element comparer to be used to order elements in the tree.</param>
            <param name="allowDuplicates">
              Pass <c>true</c> to allow duplicate elements 
              in the tree; <c>false</c> otherwise.</param>
            
        </member>
        <member name="T:Accord.Collections.KeyValuePairComparer`2">
            <summary>
              Vanilla key-based comparer for <see cref="T:System.Collections.Generic.KeyValuePair`2"/>.
            </summary>
            
            <typeparam name="TKey">The key type in the key-value pair.</typeparam>
            <typeparam name="TValue">The value type in the key-value pair.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.KeyValuePairComparer`2.#ctor(System.Collections.Generic.IComparer{`0})">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.Collections.KeyValuePairComparer`2"/> class.
            </summary>
            
            <param name="keyComparer">The comparer to be used to compare keys.</param>
            
        </member>
        <member name="M:Accord.Collections.KeyValuePairComparer`2.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.Collections.KeyValuePairComparer`2"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.KeyValuePairComparer`2.Compare(System.Collections.Generic.KeyValuePair{`0,`1},System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Compares two objects and returns a value indicating whether 
              one is less than, equal to, or greater than the other.
            </summary>
            
            <param name="x">The first object to compare.</param>
            <param name="y">The second object to compare.</param>
            
        </member>
        <member name="M:Accord.Collections.KeyValuePairComparer`2.Compare(`0,`0)">
            <summary>
              Compares two objects and returns a value indicating whether 
              one is less than, equal to, or greater than the other.
            </summary>
            
            <param name="x">The first object to compare.</param>
            <param name="y">The second object to compare.</param>
            
        </member>
        <member name="P:Accord.Collections.KeyValuePairComparer`2.Default">
            <summary>
               Returns a default sort order comparer for the
               key-value pair specified by the generic argument.
            </summary>
            
        </member>
        <member name="T:Accord.Collections.TwoWayDictionary`2">
            <summary>
              Two-way dictionary for efficient lookups by both key and value. This
              can be used to represent a one-to-one relation among two object types.
            </summary>
            
            <typeparam name="TFirst">The type of right keys in the dictionary.</typeparam>
            <typeparam name="TSecond">The type of left keys in the dictionary.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.Collections.TwoWayDictionary`2"/> class
              that is empty, has the default initial capacity, and uses the default equality comparer
              for the key type.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.#ctor(System.Int32)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.Collections.TwoWayDictionary`2"/> class
              that is empty, has the specified initial capacity, and uses the default equality comparer
              for the key type.
            </summary>
            
            <param name="capacity">The initial number of elements that this dictionary can contain.</param>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
            <summary> 
              Initializes a new instance of the <see cref="T:Accord.Collections.TwoWayDictionary`2"/> class
              that contains elements copied from the specified dictionary and uses the default equality
              comparer for the key type.
            </summary>
            
            <param name="dictionary">The dictionary whose elements are copied to the new <see cref="T:Accord.Collections.TwoWayDictionary`2"/>.</param>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.Reverse">
            <summary>
              Gets the reverse dictionary that maps values back to keys.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.Count">
            <summary>
              Gets the number of elements contained in this <see cref="T:Accord.Collections.TwoWayDictionary`2"/>.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.System#Collections#ICollection#SyncRoot">
            <summary>
              Gets an object that can be used to synchronize access to the <see cref="T:Accord.Collections.TwoWayDictionary`2"/>.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.System#Collections#ICollection#IsSynchronized">
            <summary>
              Gets a value indicating whether access to the <see cref="T:Accord.Collections.TwoWayDictionary`2"/> is synchronized (thread safe).
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.System#Collections#IDictionary#IsFixedSize">
            <summary>
              Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> object has a fixed size.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.IsReadOnly">
            <summary>
              Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.Item(`0)">
            <summary>
              Gets or sets the element with the specified key.
            </summary>
            
            <param name="key">The left key.</param>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.System#Collections#IDictionary#Item(System.Object)">
            <summary>
              Gets or sets the element with the specified key.
            </summary>
            
            <param name="key">The left key.</param>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.Keys">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.System#Collections#IDictionary#Keys">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TFirst,TSecond}#Keys">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.Values">
            <summary>
             Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.System#Collections#IDictionary#Values">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.TwoWayDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TFirst,TSecond}#Values">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the collection.
            </summary>
            
            <returns>
              A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
              Returns an enumerator that iterates through a collection.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.System#Collections#IDictionary#GetEnumerator">
            <summary>
              Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the <see cref="T:System.Collections.IDictionary" /> object.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the <see cref="T:System.Collections.IDictionary" /> object.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.Add(`0,`1)">
            <summary>
              Adds an element with the provided key and value to the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
            <param name="key">The object to use as the key of the element to add.</param>
            <param name="value">The object to use as the value of the element to add.</param>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
            <summary>
              Adds an element with the provided key and value to the <see cref="T:System.Collections.IDictionary" /> object.
            </summary>
            
            <param name="key">The <see cref="T:System.Object" /> to use as the key of the element to add.</param>
            <param name="value">The <see cref="T:System.Object" /> to use as the value of the element to add.</param>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TFirst,TSecond}}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            
            <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.ContainsKey(`0)">
            <summary>
              Determines whether the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key.
            </summary>
            
            <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</param>
            
            <returns>
              true if the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the key; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TFirst,TSecond}}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific value.
            </summary>
            
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            
            <returns>
              true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.TryGetValue(`0,`1@)">
            <summary>
              Gets the value associated with the specified key.
            </summary>
            
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
            
            <returns>
              true if the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.Remove(`0)">
            <summary>
              Removes the element with the specified key from the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
            <param name="key">The key of the element to remove.</param>
            
            <returns>
              true if the element is successfully removed; otherwise, false.  This method also returns false if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
            <summary>
              Removes the element with the specified key from the <see cref="T:System.Collections.IDictionary" /> object.
            </summary>
            
            <param name="key">The key of the element to remove.</param>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TFirst,TSecond}}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            
            <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            
            <returns>
              true if <paramref name="item" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> is not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
            <summary>
              Determines whether the <see cref="T:System.Collections.IDictionary" /> object contains an element with the specified key.
            </summary>
            
            <param name="key">The key to locate in the <see cref="T:System.Collections.IDictionary" /> object.</param>
            
            <returns>
             true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.TwoWayDictionary`2.Clear">
            <summary>
              Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            
        </member>
        <member name="T:Accord.Collections.ReadOnlyKeyedCollection`2">
            <summary>
              Read-only keyed collection wrapper.
            </summary>
            
            <remarks>
              This collection implements a read-only keyed collection. Read-only collections
              can not be changed once they are created and are useful for presenting information
              to the user without allowing alteration. A keyed collection is a collection whose 
              elements can be retrieved by key or by index.
            </remarks>
            
            <typeparam name="TKey">The types of the keys in the dictionary.</typeparam>
            <typeparam name="TValue">The type of values in the dictionary.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.#ctor(System.Collections.Generic.IList{`1})">
            <summary>
              Initializes a new instance of the 
              <see cref="T:Accord.Collections.ReadOnlyKeyedCollection`2"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.GetKeyForItem(`1)">
            <summary>
              When implemented in a derived class, extracts the key from the specified element.
            </summary>
            
            <param name="item">The element from which to extract the key.</param>
            
            <returns>The key for the specified element.</returns>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{TKey,TValue}}#GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the collection.
            </summary>
            
            <returns>
              A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.ContainsKey(`0)">
            <summary>
              Determines whether the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key.
            </summary>
            
            <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</param>
            
            <returns>
              true if the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the key; otherwise, false.
            </returns>
            
        </member>
        <member name="P:Accord.Collections.ReadOnlyKeyedCollection`2.Keys">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
            <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
            
        </member>
        <member name="P:Accord.Collections.ReadOnlyKeyedCollection`2.Values">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
            <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.TryGetValue(`0,`1@)">
            <summary>
              Gets the value associated with the specified key.
            </summary>
            
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
            
            <returns>
              true if the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key; otherwise, false.
            </returns>
            
        </member>
        <member name="P:Accord.Collections.ReadOnlyKeyedCollection`2.Item(`0)">
            <summary>
              Gets or sets the element with the specified key.
            </summary>
            
            <param name="key">The key.</param>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific value.
            </summary>
            
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            
            <returns>
              true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
            <summary>
              Copies the elements of the ICollection to an Array, starting at a particular Array index.
            </summary>
            
            <param name="array">The one-dimensional Array that is the destination of the elements copied from ICollection. The Array must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.Add(`0,`1)">
            <summary>
              This method is not supported, as this is a read-only collection.
            </summary>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.Add(System.Collections.Generic.KeyValuePair{`1,`0})">
            <summary>
              This method is not supported, as this is a read-only collection.
            </summary>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.Remove(`0)">
            <summary>
              This method is not supported, as this is a read-only collection.
            </summary>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.Add(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              This method is not supported, as this is a read-only collection.
            </summary>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              This method is not supported, as this is a read-only collection.
            </summary>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.Insert(System.Int32,`1)">
            <summary>
              This method is not supported, as this is a read-only collection.
            </summary>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.RemoveAt(System.Int32)">
            <summary>
              This method is not supported, as this is a read-only collection.
            </summary>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="M:Accord.Collections.ReadOnlyKeyedCollection`2.Clear">
            <summary>
              Not supported.
            </summary>
            
            <exception cref="T:System.NotSupportedException">This collection is read-only</exception>
            
        </member>
        <member name="P:Accord.Collections.ReadOnlyKeyedCollection`2.IsReadOnly">
            <summary>
              Returns true.
            </summary>
            
        </member>
        <member name="T:Accord.Collections.RedBlackTreeDictionary`2">
            <summary>
              Sorted dictionary based on a <see cref="T:Accord.Collections.RedBlackTree`1">red-black tree</see>.
            </summary>
            
            <typeparam name="TKey">The type of keys in the collection.</typeparam>
            <typeparam name="TValue">The type of the values in the collection</typeparam>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.#ctor">
            <summary>
              Creates a new <see cref="T:Accord.Collections.RedBlackTreeDictionary`2"/> 
              using the default comparer for the <typeparamref name="TKey">key
              type</typeparamref>.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
            <summary>
              Creates a new <see cref="T:Accord.Collections.RedBlackTreeDictionary`2"/>.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.Add(`0,`1)">
            <summary>
              Adds an element with the provided key and value to the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            
            <param name="key">The object to use as the key of the element to add.</param>
            <param name="value">The object to use as the value of the element to add.</param>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.Add(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Adds an element with the provided key and value to the dictionary.
            </summary>
            
            <param name="item">
              The <see cref="T:System.Collections.Generic.KeyValuePair`2">key-value pair</see> 
              containing the desired key and the value to be added.
            </param>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.Remove(`0)">
            <summary>
              Removes the element with the specified key from the dictionary.
            </summary>
            
            <param name="key">The key of the element to remove.</param>
            
            <returns>
              <c>true</c> if the element is successfully removed; otherwise, false. 
              This method also returns false if <paramref name="key" /> was not found 
              in the original dictionary.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Removes the first occurrence of a specific object from the dictionary.
            </summary>
            
            <param name="item">The object to remove from the dictionary.</param>
            
            <returns>
              <c>true</c> if <paramref name="item" /> was successfully removed from 
              the dictionary; otherwise, false. This method also returns false if 
              <paramref name="item" /> is not found in the original dictionary.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.ContainsKey(`0)">
            <summary>
              Determines whether the dictionary contains an element with the specified key.
            </summary>
            
            <param name="key">The key to locate in the dictionary.</param>
            
            <returns>
              <c>true</c> if the dictionary contains an element with the key; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Determines whether the dictionary contains a specific value.
            </summary>
            
            <param name="item">The object to locate in the dictionary.</param>
            
            <returns>
              <c>true</c> if <paramref name="item" /> is found in the dictionary; otherwise, false.
            </returns>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTreeDictionary`2.Keys">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection{T}" /> 
              containing the keys of the <see cref="T:Accord.Collections.RedBlackTreeDictionary`2"/>.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTreeDictionary`2.Values">
            <summary>
              Gets an <see cref="T:System.Collections.Generic.ICollection{T}" /> 
              containing the values of the <see cref="T:Accord.Collections.RedBlackTreeDictionary`2"/>.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.TryGetValue(`0,`1@)">
            <summary>
              Gets the value associated with the specified key.
            </summary>
            
            <param name="key">The key whose value to get.</param>
            <param name="value">
              When this method returns, the value associated with the specified key, 
              if the key is found; otherwise, the default value for the type of the 
              <paramref name="value" /> parameter. This parameter is passed 
              uninitialized.
            </param>
            
            <returns>
              <c>true</c> if the dictionary contains an element with the specified key; otherwise, false.
            </returns>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTreeDictionary`2.Item(`0)">
            <summary>
              Gets or sets the element with the specified key.
            </summary>
            
            <param name="key">The key.</param>
            
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">The requested key was not found in the present tree.</exception>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.Clear">
            <summary>
              Removes all elements from the dictionary.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
            <summary>
              Copies the elements of this dictionary to an array, starting at a particular array index.
            </summary>
            
            <param name="array">
              The one-dimensional Array that is the destination of the elements
              copied from ICollection. The array must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTreeDictionary`2.Count">
            <summary>
              Gets the number of elements on this dictionary.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTreeDictionary`2.IsReadOnly">
            <summary>
              Gets a value indicating whether this instance is read only.
            </summary>
            
            <value>
              Returns <c>false</c>.
            </value>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the dictionary.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.Generic.IEnumerator{T}"/>
              object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
              Returns an enumerator that iterates through the dictionary.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.Min">
            <summary>
              Gets the pair with the minimum <c>key</c> stored in the dictionary.
            </summary>
            
            <returns>
              The <see cref="T:System.Collections.Generic.KeyValuePair`2"/> with
              the minimum key present in the dictionary.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.Max">
            <summary>
              Gets the pair with the maximum <c>key</c> stored in the dictionary.
            </summary>
            
            <returns>
              The <see cref="T:System.Collections.Generic.KeyValuePair`2"/> with
              the minimum key present in the dictionary.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.GetPrevious(`0)">
            <summary>
              Gets the next key-value pair in the dictionary whose key is
              the immediate ancestor of the given <paramref name="key"/>.
            </summary>
            
            <param name="key">The key whose ancestor must be found.</param>
            
            <returns>
              The key-value pair whose key is the immediate ancestor of <paramref name="key"/>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.TryGetPrevious(`0,System.Collections.Generic.KeyValuePair{`0,`1}@)">
            <summary>
              Gets the next key-value pair in the dictionary whose key is
              the immediate ancestor of the given <paramref name="key"/>.
            </summary>
            
            <param name="key">The key whose ancestor must be found.</param>
            <param name="prev">
              The key-value pair whose key is the immediate ancestor of
              <paramref name="key"/>, returned as an out parameter.
            </param>
            
            <returns>
              True if there was an ancestor in the dictionary; false otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.GetNext(`0)">
            <summary>
              Gets the next key-value pair in the dictionary whose key is
              the immediate successor to the given <paramref name="key"/>.
            </summary>
            
            <param name="key">The key whose successor must be found.</param>
            
            <returns>
              The key-value pair whose key is the immediate successor of <paramref name="key"/>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeDictionary`2.TryGetNext(`0,System.Collections.Generic.KeyValuePair{`0,`1}@)">
            <summary>
              Gets the next key-value pair in the dictionary whose key is
              the immediate successor to the given <paramref name="key"/>.
            </summary>
            
            <param name="key">The key whose successor must be found.</param>
            <param name="next">
              The key-value pair whose key is the immediate sucessor of
              <paramref name="key"/>, returned as an out parameter.
            </param>
            
            <returns>
              True if there was a successor in the dictionary; false otherwise.
            </returns>
            
        </member>
        <member name="T:Accord.Collections.RedBlackTreeNodeType">
            <summary>
              Possible node colors for <see cref="T:Accord.Collections.RedBlackTree`1"/>s.
            </summary>
            
        </member>
        <member name="F:Accord.Collections.RedBlackTreeNodeType.Red">
            <summary>
              Red node.
            </summary>
            
        </member>
        <member name="F:Accord.Collections.RedBlackTreeNodeType.Black">
            <summary>
              Black node.
            </summary>
            
        </member>
        <member name="T:Accord.Collections.RedBlackTreeNode`1">
            <summary>
              <see cref="T:Accord.Collections.RedBlackTree`1"/> node.
            </summary>
            
            <typeparam name="T">The type of the value to be stored.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeNode`1.#ctor">
            <summary>
              Constructs a new empty node.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeNode`1.#ctor(`0)">
            <summary>
              Constructs a node containing the given <param name="value"/>.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTreeNode`1.Parent">
            <summary>
              Gets or sets a reference to this node's parent node.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTreeNode`1.Color">
            <summary>
              Gets or sets this node's color.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTreeNode`1.Value">
            <summary>
              Gets or sets the value associated with this node.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeNode`1.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="T:Accord.Collections.RedBlackTreeNode`2">
            <summary>
              <see cref="T:Accord.Collections.RedBlackTree`1"/> node.
            </summary>
            
            <typeparam name="TKey">The type of the key that identifies the value.</typeparam>
            <typeparam name="TValue">The type of the values stored in this node.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeNode`2.#ctor">
            <summary>
              Constructs a new empty node.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeNode`2.#ctor(`0,`1)">
            <summary>
              Constructs a new node containing the given <param name="key">
              key</param> and <param name="value">value</param> pair.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTreeNode`2.#ctor(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
              Constructs a new node containing the given
              <param name="item">key and value pair</param>.
            </summary>
            
        </member>
        <member name="T:Accord.Collections.RedBlackTree`1">
            <summary>
              Red-black tree.
            </summary>
            
            <remarks>
            <para>
              A red–black tree is a data structure which is a type of self-balancing binary 
              search tree. Balance is preserved by painting each node of the tree with one of
              two colors (typically called 'red' and 'black') in a way that satisfies certain 
              properties, which collectively constrain how unbalanced the tree can become in 
              the worst case. When the tree is modified, the new tree is subsequently rearranged
              and repainted to restore the coloring properties. The properties are designed in 
              such a way that this rearranging and recoloring can be performed efficiently.</para>
              
            <para>
              The balancing of the tree is not perfect but it is good enough to allow it to 
              guarantee searching in O(log n) time, where n is the total number of elements 
              in the tree. The insertion and deletion operations, along with the tree rearrangement 
              and recoloring, are also performed in O(log n) time. </para>
              
            <para>
              Tracking the color of each node requires only 1 bit of information per node because
              there are only two colors. The tree does not contain any other data specific to its
              being a red–black tree so its memory footprint is almost identical to a classic 
              (uncolored) binary search tree. </para>
              
            <para>
              References:
              <list type="bullet">
                <item><description><a href="http://ab-initio.mit.edu/nlopt">
                  Steven G. Johnson, The NLopt nonlinear-optimization package, 
                  http://ab-initio.mit.edu/nlopt </a></description></item>
                <item><description><a href="http://en.wikipedia.org/wiki/Red%E2%80%93black_tree">
                  Wikipedia, The Free Encyclopedia. Red-black tree. Available on:
                  http://en.wikipedia.org/wiki/Red%E2%80%93black_tree </a></description></item>
               </list></para>
            </remarks>
            
            <typeparam name="T">The type of the value to be stored.</typeparam>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.#ctor">
            <summary>
              Constructs a new <see cref="T:Accord.Collections.RedBlackTree`1"/> using the
              default <see cref="T:System.Collections.Generic.IComparer`1"/> for type <typeparamref name="T"/>.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.#ctor(System.Collections.Generic.IComparer{`0})">
            <summary>
              Constructs a new <see cref="T:Accord.Collections.RedBlackTree`1"/> using 
              the provided <see cref="T:System.Collections.Generic.IComparer`1"/> implementation.
            </summary>
            
            <param name="comparer">
              The element comparer to be used to order elements in the tree.</param>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.#ctor(System.Boolean)">
            <summary>
              Constructs a new <see cref="T:Accord.Collections.RedBlackTree`1"/> using the
              default <see cref="T:System.Collections.Generic.IComparer`1"/> for type <typeparamref name="T"/>.
            </summary>
            
            <param name="allowDuplicates">
              Pass <c>true</c> to allow duplicate elements 
              in the tree; <c>false</c> otherwise.</param>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.#ctor(System.Collections.Generic.IComparer{`0},System.Boolean)">
            <summary>
              Constructs a new <see cref="T:Accord.Collections.RedBlackTree`1"/> using 
              the provided <see cref="T:System.Collections.Generic.IComparer`1"/> implementation.
            </summary>
            
            <param name="comparer">
              The element comparer to be used to order elements in the tree.</param>
            <param name="allowDuplicates">
              Pass <c>true</c> to allow duplicate elements 
              in the tree; <c>false</c> otherwise.</param>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTree`1.Count">
            <summary>
              Gets the number of nodes contained in this red-black tree.
            </summary>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTree`1.Comparer">
            <summary>
              Gets the <see cref="T:System.Collections.Generic.IComparer`1"/> for this red black tree.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Clear">
            <summary>
              Removes all nodes from the tree.
            </summary>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Add(`0)">
            <summary>
              Adds a new item to the tree. If the element already
              belongs to this tree, no new element will be added.
            </summary>
            
            <param name="item">The item to be added.</param>
            
            <returns>The node containing the added item.</returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Add(Accord.Collections.RedBlackTreeNode{`0})">
            <summary>
              Adds a new item to the tree. If the element already
              belongs to this tree, no new element will be added.
            </summary>
            
            <param name="item">The node to be added to the tree.</param>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.System#Collections#Generic#ICollection{T}#Remove(`0)">
            <summary>
              Attempts to remove an element from the tree.
            </summary>
            
            <param name="item">The item to be removed.</param>
            
            <returns>
              <c>True</c> if the element was in the tree and was removed; false otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.System#Collections#Generic#ICollection{Accord#Collections#RedBlackTreeNode{T}}#Remove(Accord.Collections.RedBlackTreeNode{`0})">
            <summary>
              Removes a node from the tree.
            </summary>
            
            <param name="item">The node to be removed.</param>
            
            <returns>
              <c>True</c> if the element was in the tree and was removed; false otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Remove(`0)">
            <summary>
              Removes a node from the tree.
            </summary>
            
            <param name="item">The key of the node to be removed.</param>
            
            <returns>
              A reference to the removed node, if the item was in the tree; otherwise, <c>null</c>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Remove(Accord.Collections.RedBlackTreeNode{`0})">
            <summary>
              Removes a node from the tree.
            </summary>
            
            <param name="node">The node to be removed.</param>
            
            <returns>
              A reference to the removed node.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.CopyTo(Accord.Collections.RedBlackTreeNode{`0}[],System.Int32)">
            <summary>
              Copies the nodes of this tree to an array, starting at a
              particular <paramref name="arrayIndex">array index</paramref>.
            </summary>
            
            <param name="array">
              The one-dimensional array that is the destination of the elements
              copied from this tree. The array must have zero-based indexing.
            </param>
            
            <param name="arrayIndex">
              The zero-based index in <paramref name="array"/> at which copying begins.
            </param>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.CopyTo(`0[],System.Int32)">
            <summary>
              Copies the elements of this tree to an array, starting at a
              particular <paramref name="arrayIndex">array index</paramref>.
            </summary>
            
            <param name="array">
              The one-dimensional array that is the destination of the elements
              copied from this tree. The array must have zero-based indexing.
            </param>
            
            <param name="arrayIndex">
              The zero-based index in <paramref name="array"/> at which copying begins.
            </param>
            
        </member>
        <member name="P:Accord.Collections.RedBlackTree`1.IsReadOnly">
            <summary>
              Gets a value indicating whether this instance is read only. 
              In a <see cref="T:Accord.Collections.RedBlackTree`1"/>, this returns false.
            </summary>
            
            <value>
               Returns <c>false</c>.
            </value>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through this tree in-order.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator"/> object that can
              be used to traverse through this tree using in-order traversal.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
              Returns an enumerator that iterates through this tree in-order.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator"/> object that can
              be used to traverse through this tree using in-order traversal.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Contains(`0)">
            <summary>
              Determines whether this tree contains the specified item.
            </summary>
            
            <param name="item">The item to be looked for.</param>
            
            <returns>
              <c>true</c> if the element was found inside the tree; otherwise, <c>false</c>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Contains(Accord.Collections.RedBlackTreeNode{`0})">
            <summary>
              Determines whether this tree contains the specified item.
            </summary>
            
            <param name="item">The item to be looked for.</param>
            
            <returns>
              <c>true</c> if the element was found inside the tree; otherwise, <c>false</c>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Find(`0)">
            <summary>
              Attempts to find a node that contains the specified key.
            </summary>
            
            <param name="item">The key whose node is to be found.</param>
            
            <returns>
              A <see cref="T:Accord.Collections.RedBlackTreeNode`1"/> containing the desired <paramref name="item"/>
              if it is present in the dictionary; otherwise, returns <c>null</c>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.FindLessThanOrEqualTo(Accord.Collections.RedBlackTreeNode{`0},`0)">
            <summary>
             Finds the greatest point in the subtree rooted at <paramref name="node"/>
             that is less than or equal to (&lt;=) <c>k</c>. In other words, finds either
             <c>k</c> or a number immediately below it.
            </summary>
            
            <param name="node">The subtree where search will take place.</param>
            <param name="value">A reference value to be found.</param>
            
            <returns>
              The node containing the given value <paramref name="value"/> or 
              its immediately smaller neighboring number present in the tree.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.FindLessThanOrEqualTo(`0)">
            <summary>
             Finds the greatest point in the <see cref="T:Accord.Collections.RedBlackTree`1">
             tree</see> that is less than or equal to (&lt;=) <c>k</c>.
             In other words, finds either <c>k</c> or a number immediately
             below it.
            </summary>
            
            <param name="value">A reference for the value to be found.</param>
            
            <returns>
              The node containing the given value <paramref name="value"/> or 
              its immediately smaller neighboring number present in the tree.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.FindLessThan(Accord.Collections.RedBlackTreeNode{`0},`0)">
            <summary>
             Finds the greatest point in the subtree rooted at <paramref name="node"/>
             that is less than (&lt;) <c>k</c>. In other words, finds a number stored in
             the tree that is immediately below <c>k</c>.
            </summary>
            
            <param name="node">The subtree where search will take place.</param>
            <param name="value">A reference value to be found.</param>
            
            <returns>
              The node containing an element that is immediately below <paramref name="value"/>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.FindLessThan(`0)">
            <summary>
             Finds the greatest point in the <see cref="T:Accord.Collections.RedBlackTree`1">
             tree</see> that is less than (&lt;) <c>k</c>. In other words, finds
             a number stored in the tree that is immediately below <c>k</c>.
            </summary>
            
            <param name="value">A reference value to be found.</param>
            
            <returns>
              The node containing an element that is immediately below <paramref name="value"/>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.FindGreaterThan(Accord.Collections.RedBlackTreeNode{`0},`0)">
            <summary>
             Finds the smallest point in the subtree rooted at <paramref name="node"/>
             that is greater than (>) <c>k</c>. In other words, finds a number stored in
             the tree that is immediately above <c>k</c>.
            </summary>
            
            <param name="node">The subtree where search will take place.</param>
            <param name="value">A reference value to be found.</param>
            
            <returns>
              The node containing an element that is immediately below <paramref name="value"/>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.FindGreaterThan(`0)">
            <summary>
             Finds the smallest point in the in the <see cref="T:Accord.Collections.RedBlackTree`1">
             tree</see> that is greater than (>) <c>k</c>. In other words, finds a
             number stored in the tree that is immediately above <c>k</c>.
            </summary>
            
            <param name="value">A reference value to be found.</param>
            
            <returns>
              The node containing an element that is immediately below <paramref name="value"/>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Min">
            <summary>
              Finds the minimum element stored in the tree.
            </summary>
            
            <returns>
              The <see cref="T:Accord.Collections.RedBlackTreeNode`1"/> that 
              holds the minimum element in the tree.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Max">
            <summary>
              Finds the maximum element stored in the tree.
            </summary>
            
            <returns>
              The <see cref="T:Accord.Collections.RedBlackTreeNode`1"/> that
              holds the maximum element in the tree.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.GetNextNode(Accord.Collections.RedBlackTreeNode{`0})">
            <summary>
              Gets the node that contains the next in-order value coming 
              after the value contained in the given <paramref name="node"/>.
            </summary>
            
            <param name="node">The current node.</param>
            
            <returns>
              The node that contains a value that is immediately greater than
              the current value contained in the given <paramref name="node"/>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.GetPreviousNode(Accord.Collections.RedBlackTreeNode{`0})">
            <summary>
              Gets the node that contains the previous in-order value coming
              before the value contained in the given <paramref name="node"/>.
            </summary>
            
            <param name="node">The current node.</param>
            
            <returns>
              The node that contains a value that is immediately less than
              the current value contained in the given <paramref name="node"/>.
            </returns>
            
        </member>
        <member name="M:Accord.Collections.RedBlackTree`1.Resort(Accord.Collections.RedBlackTreeNode{`0})">
            <summary>
              Forces a re-balance of the tree by removing and inserting the same node.
            </summary>
            
            <param name="node">The node to be re-balanced.</param>
            
            <returns>The same node, or a new one if it had to be recreated.</returns>
            
        </member>
        <member name="T:Accord.Diagnostics.Debug">
            <summary>
              Temporary internal framework class for handling debug assertions
              while inside unit tests. Will be removed in a future release.
            </summary>
            
        </member>
        <member name="M:Accord.Diagnostics.Debug.Assert(System.Boolean,System.String)">
            <summary>
              Throws an exception if a condition is false.
            </summary>
            
        </member>
        <member name="T:Accord.IFeatureDescriptor`1">
            <summary>
              Common interface for feature descriptors.
            </summary>
            
        </member>
        <member name="P:Accord.IFeatureDescriptor`1.Descriptor">
            <summary>
              Gets or sets the descriptor vector associated with this point.
            </summary>
            
        </member>
        <member name="T:Accord.IFeatureExtractor`2">
            <summary>
              Common interface for feature extractors.
            </summary>
            
        </member>
        <member name="T:Accord.InterlockedEx">
            <summary>
              An extension of <see cref="T:System.Threading.Interlocked"/> providing
              atomic operations such as Add and Increment to floating point numbers.
            </summary>
            
        </member>
        <member name="M:Accord.InterlockedEx.Add(System.Double@,System.Double)">
            <summary>
              Adds two 32-bit floating point values and replaces the first
              double value with their sum, as an atomic operation.
            </summary>
            
            <param name="location1">The first variable to be added.</param>
            <param name="value">The second variable to be added.</param>
            
            <returns>The updated value of the first variable.</returns>
            
        </member>
        <member name="M:Accord.InterlockedEx.Increment(System.Double@)">
            <summary>
              Increments a specified variable and stores the result, as an atomic operation.
            </summary>
            
            <param name="location1">The variable to be incremented.</param>
            
            <returns>The updated value of the variable.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IClassifier">
            <summary>
              Common interface for classification models. Classification models
              learn how to produce a class-label (or a set of class labels) <c>y</c>
              from an input vector <c>x</c>.
            </summary>
            
        </member>
        <member name="P:Accord.MachineLearning.IClassifier.NumberOfClasses">
            <summary>
              Gets or sets the number of classes expected and recognized by the classifier.
            </summary>
            
        </member>
        <member name="T:Accord.MachineLearning.IClassifier`2">
            <summary>
              Common interface for classification models. Classification models
              learn how to produce a class-label (or a set of class labels) <c>y</c>
              from an input vector <c>x</c>.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IClassifier`2.Decide(`0)">
            <summary>
              Computes a class-label decision for a given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vector that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            
            <returns>A class-label that best described <paramref name="input"/> according
            to this classifier.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IClassifier`2.Decide(`0[])">
            <summary>
              Computes class-label decisions for each vector in the given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vectors that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            
            <returns>The class-labels that best describe each <paramref name="input"/> 
              vectors according to this classifier.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IClassifier`2.Decide(`0[],`1[])">
            <summary>
              Computes class-label decisions for each vector in the given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vectors that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            <param name="result">The location where to store the class-labels.</param>
            
            <returns>The class-labels that best describe each <paramref name="input"/> 
              vectors according to this classifier.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassClassifier`2">
             <summary>
               Common interface for multi-class models. Classification models
               learn how to produce a class-label <c>y</c> from an input vector <c>x</c>.
             </summary>
             
             <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
             <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassClassifier`1">
            <summary>
              Common interface for multi-class models. Classification models
              learn how to produce a class-label <c>y</c> from an input vector <c>x</c>.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassClassifier`1.Decide(`0)">
            <summary>
              Computes a class-label decision for a given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vector that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            
            <returns>A class-label that best described <paramref name="input"/> according
            to this classifier.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassClassifier`1.Decide(`0[])">
            <summary>
              Computes class-label decisions for each vector in the given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vectors that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            
            <returns>The class-labels that best describe each <paramref name="input"/> 
              vectors according to this classifier.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassClassifier`1.ToMultilabel">
            <summary>
              Views this instance as a multi-label classifier,
              giving access to more advanced methods, such as the prediction
              of one-hot vectors.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1"/>.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassClassifier">
            <summary>
              Common interface for multi-class models. Classification models
              learn how to produce a class-label <c>y</c> from an input vector <c>x</c>.
            </summary>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassScoreClassifierBase`2">
            <summary>
              Common interface for score-based multi-class classifiers. A multi-class
              classifier can predict to which class an instance belongs based
              on a decision score (a real number) that measures the association of the
              input with each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassScoreClassifierBase`2.Score(`0[],`1[]@)">
            <summary>
              Predicts a class label for each input vector, returning a
              numerical score measuring the strength of association of the
              input vector to the most strongly related class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels predicted for each input
              vector, as predicted by the classifier.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassScoreClassifierBase`2.Score(`0[],`1[]@,System.Double[])">
            <summary>
              Predicts a class label for each input vector, returning a
              numerical score measuring the strength of association of the
              input vector to the most strongly related class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels predicted for each input
              vector, as predicted by the classifier.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassOutScoreClassifier`2">
            <summary>
              Common interface for score-based multi-class classifiers. A multi-class
              classifier can predict to which class an instance belongs based
              on a decision score (a real number) that measures the association of the
              input with each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassOutScoreClassifier`2.Score(`0,`1@)">
            <summary>
              Predicts a class label for the input vector, returning a
              numerical score measuring the strength of association of the
              input vector to its most strongly related class.
            </summary>
            
            <param name="input">The input vector.</param>
            <param name="decision">The class label predicted by the classifier.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassRefScoreClassifier`2">
            <summary>
              Common interface for score-based multi-class classifiers. A multi-class
              classifier can predict to which class an instance belongs based
              on a decision score (a real number) that measures the association of the
              input with each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassScoreClassifier`2">
            <summary>
              Common interface for score-based multi-class classifiers. A multi-class
              classifier can predict to which class an instance belongs based
              on a decision score (a real number) that measures the association of the
              input with each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassScoreClassifier`1">
            <summary>
              Common interface for score-based multi-class classifiers. A multi-class
              classifier can predict to which class an instance belongs based
              on a decision score (a real number) that measures the association of the
              input with each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassScoreClassifier`1.Decide(`0)">
            <summary>
              Computes a class-label decision for a given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vector that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            
            <returns>A class-label that best described <paramref name="input"/> according
            to this classifier.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassScoreClassifier`1.Decide(`0[])">
            <summary>
              Computes class-label decisions for each vector in the given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vectors that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            
            <returns>The class-labels that best describe each <paramref name="input"/> 
              vectors according to this classifier.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassScoreClassifier`1.Score(`0)">
            <summary>
              Computes a numerical score measuring the association between
              the given <paramref name="input"/> vector and its most strongly
              associated class (as predicted by the classifier).
            </summary>
            
            <param name="input">The input vector.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassScoreClassifier`1.Score(`0[])">
            <summary>
              Computes a numerical score measuring the association between
              each of the given <paramref name="input"/> vectors and their
              respective most strongly associated classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassScoreClassifier`1.Score(`0[],System.Double[])">
             <summary>
               Computes a numerical score measuring the association between
               each of the given <paramref name="input"/> vectors and their
               respective most strongly associated classes.
             </summary>
             
             <param name="input">A set of input vectors.</param>
             <param name="result">An array where the result will be stored, 
               avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassScoreClassifier`1.ToMultilabel">
            <summary>
              Views this instance as a multi-label distance classifier,
              giving access to more advanced methods, such as the prediction
              of one-hot vectors.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMultilabelScoreClassifier`1"/>.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassLikelihoodClassifierBase`2">
            <summary>
              Common interface for generative multi-class classifiers. A multi-class
              classifier can predicts a class label based on an input instance vector.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifierBase`2.Probability(`0[],`1[]@)">
            <summary>
              Predicts a class label for each input vector, returning the
              probability that each vector belongs to its predicted class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifierBase`2.Probability(`0[],`1[]@,System.Double[])">
            <summary>
              Predicts a class label for each input vector, returning the
              probability that each vector belongs to its predicted class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            <param name="result">An array where the probabilities will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifierBase`2.LogLikelihood(`0[],`1[]@)">
            <summary>
              Predicts a class label for each input vector, returning the
              log-likelihood that each vector belongs to its predicted class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifierBase`2.LogLikelihood(`0[],`1[]@,System.Double[])">
            <summary>
              Predicts a class label for each input vector, returning the
              log-likelihood that each vector belongs to its predicted class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            <param name="result">An array where the log-likelihoods will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassOutLikelihoodClassifier`2">
            <summary>
              Common interface for generative multi-class classifiers. A multi-class
              classifier can predicts a class label based on an input instance vector.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassOutLikelihoodClassifier`2.Probability(`0,`1@)">
             <summary>
               Predicts a class label for the given input vector, returning the
               probability that the input vector belongs to its predicted class.
             </summary>
             
             <param name="input">The input vector.</param>
             <param name="decision">The class label predicted by the classifier.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassOutLikelihoodClassifier`2.LogLikelihood(`0,`1@)">
             <summary>
               Predicts a class label vector for the given input vector, returning the
               log-likelihood that the input vector belongs to its predicted class.
             </summary>
            
             <param name="input">The input vector.</param>
             <param name="decision">The class label predicted by the classifier.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassRefLikelihoodClassifier`2">
            <summary>
              Common interface for generative multi-class classifiers. A multi-class
              classifier can predicts a class label based on an input instance vector.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassLikelihoodClassifier`2">
            <summary>
              Common interface for generative multi-class classifiers. A multi-class
              classifier can predicts a class label based on an input instance vector.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int.</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1">
            <summary>
              Common interface for generative multi-class classifiers. A multi-class
              classifier can predicts a class label based on an input instance vector.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.Decide(`0)">
            <summary>
              Computes a class-label decision for a given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vector that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            
            <returns>A class-label that best described <paramref name="input"/> according
            to this classifier.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.Decide(`0[])">
            <summary>
              Computes class-label decisions for each vector in the given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vectors that should be classified into
              one of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            
            <returns>The class-labels that best describe each <paramref name="input"/> 
              vectors according to this classifier.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.Probability(`0)">
             <summary>
               Predicts a class label for the given input vector, returning the
               probability that the input vector belongs to its predicted class.
             </summary>
             
             <param name="input">The input vector.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.Probability(`0[])">
             <summary>
               Predicts a class label for the given input vectors, returning the
               probability that the input vector belongs to its predicted class.
             </summary>
             
             <param name="input">The input vector.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.Probability(`0[],System.Double[])">
             <summary>
               Predicts a class label for the given input vectors, returning the
               probability that the input vector belongs to its predicted class.
             </summary>
             
             <param name="input">The input vector.</param>
             <param name="result">An array where the probabilities will be stored,
               avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.LogLikelihood(`0)">
             <summary>
               Predicts a class label vector for the given input vector, returning the
               log-likelihood that the input vector belongs to its predicted class.
             </summary>
            
             <param name="input">The input vector.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.LogLikelihood(`0[])">
             <summary>
               Predicts a class label vector for the given input vectors, returning the
               log-likelihood that the input vector belongs to its predicted class.
             </summary>
            
             <param name="input">The input vector.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.LogLikelihood(`0[],System.Double[])">
             <summary>
               Predicts a class label vector for the given input vectors, returning the
               log-likelihood that the input vector belongs to its predicted class.
             </summary>
            
             <param name="input">The input vector.</param>
             <param name="result">An array where the log-likelihoods will be stored,
               avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1.ToMultilabel">
            <summary>
              Views this instance as a multi-label generative classifier,
              giving access to more advanced methods, such as the prediction
              of one-hot vectors.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1"/>.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelClassifier`2">
            <summary>
              Common interface for multi-label classifiers. A multi-label classifier can
              predict the occurrence of multiple class labels at once.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelClassifier`2.Decide(`0,`1)">
            <summary>
              Computes class-label decisions for the given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vectors that should be classified as
              any of the <see cref="P:Accord.MachineLearning.ITransform.NumberOfOutputs"/> possible classes.</param>
            <param name="result">The location where to store the class-labels.</param>
            
            <returns>A set of class-labels that best describe the <paramref name="input"/> 
              vectors according to this classifier.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelClassifier`1">
            <summary>
              Common interface for multi-label classifiers. A multi-label classifier can
              predict the occurrence of multiple class labels at once.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelClassifier">
            <summary>
              Common interface for multi-label classifiers. A multi-label classifier can
              predict the occurrence of multiple class labels at once.
            </summary>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelScoreClassifierBase`2">
            <summary>
              Common interface for score-based multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once
              based on a decision score (a real number) computed for each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifierBase`2.Scores(`0[],`1[]@)">
            <summary>
              Predicts a class label vector for each input vector, returning a
              numerical score measuring the strength of association of the input vector
              to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifierBase`2.Scores(`0[],`1[]@,System.Double[][])">
            <summary>
              Predicts a class label vector for each input vector, returning a
              numerical score measuring the strength of association of the input vector
              to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelRefScoreClassifier`2">
            <summary>
              Common interface for score-based multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once
              based on a decision score (a real number) computed for each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelRefScoreClassifier`2.Scores(`0,`1@)">
            <summary>
              Predicts a class label vector for the given input vector, returning a
              numerical score measuring the strength of association of the input vector
              to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelRefScoreClassifier`2.Scores(`0,`1@,System.Double[])">
            <summary>
              Predicts a class label vector for the given input vector, returning a
              numerical score measuring the strength of association of the input vector
              to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelOutScoreClassifier`2">
            <summary>
              Common interface for score-based multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once
              based on a decision score (a real number) computed for each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelOutScoreClassifier`2.Scores(`0,`1@)">
             <summary>
               Predicts a class label vector for the given input vector, returning a
               numerical score measuring the strength of association of the input vector
               to each of the possible classes.
             </summary>
             
             <param name="input">The input vector.</param>
             <param name="decision">The class label predicted by the classifier.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelOutScoreClassifier`2.Scores(`0,`1@,System.Double[])">
            <summary>
              Predicts a class label vector for the given input vector, returning a
              numerical score measuring the strength of association of the input vector
              to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class label predicted by the classifier.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelScoreClassifier`2">
            <summary>
              Common interface for score-based multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once
              based on a decision score (a real number) computed for each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelScoreClassifier`1">
            <summary>
              Common interface for score-based multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once
              based on a decision score (a real number) computed for each class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Score(`0,System.Int32)">
            <summary>
              Computes a numerical score measuring the association between
              the given <paramref name="input"/> vector and a given 
              <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">The input vector.</param>
            <param name="classIndex">The index of the class whose score will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Score(`0[],System.Int32[])">
            <summary>
              Computes a numerical score measuring the association between
              each of the given <paramref name="input"/> vectors and the 
              given <paramref name="classIndex">class indices</paramref>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Score(`0[],System.Int32[],System.Double[])">
            <summary>
              Computes a numerical score measuring the association between
              each of the given <paramref name="input"/> vectors and the 
              given <paramref name="classIndex">class indices</paramref>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Score(`0[],System.Int32)">
            <summary>
              Computes a numerical score measuring the association between
              each of the given <paramref name="input"/> vectors and the 
              given <paramref name="classIndex">class indices</paramref>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The index of the class whose score will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Score(`0[],System.Int32,System.Double[])">
            <summary>
              Computes a numerical score measuring the association between
              each of the given <paramref name="input"/> vectors and the 
              given <paramref name="classIndex">class indices</paramref>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The index of the class whose score will be computed.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Scores(`0)">
            <summary>
              Computes a numerical score measuring the association between
              the given <paramref name="input"/> vector and each class.
            </summary>
            
            <param name="input">The input vector.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Scores(`0,System.Double[])">
            <summary>
              Computes a numerical score measuring the association between
              the given <paramref name="input"/> vector and each class.
            </summary>
            
            <param name="input">The input vector.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
              
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Scores(`0[])">
            <summary>
              Computes a numerical score measuring the association between
              each of the given <paramref name="input"/> vectors and each
              possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.Scores(`0[],System.Double[][])">
            <summary>
              Computes a numerical score measuring the association between
              each of the given <paramref name="input"/> vectors and each
              possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.ToMulticlass">
            <summary>
            Views this instance as a multi-class score-based classifier.
            </summary>
            
            <returns>
            This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassScoreClassifierBase`2" />.
            </returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelScoreClassifier`1.ToMulticlass``1">
            <summary>
            Views this instance as a multi-class score-based classifier.
            </summary>
            
            <returns>
            This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassScoreClassifierBase`2" />.
            </returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelLikelihoodClassifierBase`2">
            <summary>
              Common interface for generative multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifierBase`2.Probabilities(`0[],`1[]@)">
            <summary>
              Predicts a class label vector for each input vector, returning the
              probabilities of the input vector belonging to each possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The labels predicted by the classifier.</param>
              
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifierBase`2.Probabilities(`0[],`1[]@,System.Double[][])">
            <summary>
              Predicts a class label vector for each input vector, returning the
              probabilities of the input vector belonging to each possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The labels predicted by the classifier.</param>
            <param name="result">An array where the probabilities will be stored,
              avoiding unnecessary memory allocations.</param>
              
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifierBase`2.LogLikelihoods(`0[],`1[]@)">
            <summary>
              Predicts a class label vector for each input vector, returning the
              log-likelihoods of the input vector belonging to each possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The labels predicted by the classifier.</param>
              
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifierBase`2.LogLikelihoods(`0[],`1[]@,System.Double[][])">
            <summary>
              Predicts a class label vector for each input vector, returning the
              log-likelihoods of the input vector belonging to each possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The labels predicted by the classifier.</param>
            <param name="result">An array where the log-likelihoods will be stored,
              avoiding unnecessary memory allocations.</param>
              
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelOutLikelihoodClassifier`2">
            <summary>
              Common interface for generative multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once,
              as well as their probabilities.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelOutLikelihoodClassifier`2.Probabilities(`0,`1@)">
            <summary>
              Predicts a class label vector for the given input vector, returning the
              probabilities of the input vector belonging to each possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelOutLikelihoodClassifier`2.Probabilities(`0,`1@,System.Double[])">
            <summary>
              Predicts a class label vector for the given input vector, returning the
              probabilities of the input vector belonging to each possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelOutLikelihoodClassifier`2.LogLikelihoods(`0,`1@)">
            <summary>
              Predicts a class label vector for the given input vector, returning the
              log-likelihoods of the input vector belonging to each possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelOutLikelihoodClassifier`2.LogLikelihoods(`0,`1@,System.Double[])">
            <summary>
              Predicts a class label vector for the given input vector, returning the
              log-likelihoods of the input vector belonging to each possible class.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="decision">The class labels associated with each input
              vector, as predicted by the classifier. If passed as null, the classifier
              will create a new array.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelRefLikelihoodClassifier`2">
            <summary>
              Common interface for generative multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once,
              as well as their probabilities.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelRefLikelihoodClassifier`2.Probabilities(`0,`1@)">
             <summary>
               Predicts a class label vector for the given input vector, returning the
               probabilities of the input vector belonging to each possible class.
             </summary>
             
             <param name="input">The input vector.</param>
             <param name="decision">The class label predicted by the classifier.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelRefLikelihoodClassifier`2.Probabilities(`0,`1@,System.Double[])">
             <summary>
               Predicts a class label vector for the given input vector, returning the
               probabilities of the input vector belonging to each possible class.
             </summary>
            
             <param name="input">The input vector.</param>
             <param name="decision">The class label predicted by the classifier.</param>
             <param name="result">An array where the distances will be stored,
               avoiding unnecessary memory allocations.</param>
             
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelRefLikelihoodClassifier`2.LogLikelihoods(`0,`1@)">
             <summary>
               Predicts a class label vector for the given input vector, returning the
               log-likelihoods of the input vector belonging to each possible class.
             </summary>
            
             <param name="input">The input vector.</param>
             <param name="decision">The class label predicted by the classifier.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelRefLikelihoodClassifier`2.LogLikelihoods(`0,`1@,System.Double[])">
             <summary>
               Predicts a class label vector for the given input vector, returning the
               log-likelihoods of the input vector belonging to each possible class.
             </summary>
            
             <param name="input">The input vector.</param>
             <param name="decision">The class label predicted by the classifier.</param>
             <param name="result">An array where the distances will be stored,
               avoiding unnecessary memory allocations.</param>
             
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelLikelihoodClassifier`2">
            <summary>
              Common interface for generative multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TClasses">The data type for the class labels. Default is int[].</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1">
            <summary>
              Common interface for generative multi-label classifiers. A multi-label
              classifier can predict the occurrence of multiple class labels at once,
              as well as their probabilities.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probability(`0,System.Int32)">
            <summary>
              Computes the probability that the given input vector
              belongs to the specified <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">The input vector.</param>
            <param name="classIndex">The index of the class whose score will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probability(`0[],System.Int32[])">
            <summary>
              Computes the probability that the given input vectors
              belongs to each class specified in <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probability(`0[],System.Int32[],System.Double[])">
            <summary>
              Computes the probability that the given input vectors
              belongs to each class specified in <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probability(`0[],System.Int32)">
            <summary>
              Computes the probability that the given input vectors
              belongs to each class specified in <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probability(`0[],System.Int32,System.Double[])">
            <summary>
              Computes the probability that the given input vectors
              belongs to each class specified in <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihood(`0,System.Int32)">
            <summary>
              Computes the log-likelihood that the given input vector
              belongs to the specified <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">The input vector.</param>
            <param name="classIndex">The index of the class whose score will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihood(`0[],System.Int32[])">
            <summary>
              Computes the log-likelihood that the given input vectors
              belongs to each class specified in <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihood(`0[],System.Int32[],System.Double[])">
            <summary>
              Computes the log-likelihood that the given input vectors
              belongs to each class specified in <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihood(`0[],System.Int32)">
            <summary>
              Computes the log-likelihood that the given input vectors
              belongs to each class specified in <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihood(`0[],System.Int32,System.Double[])">
            <summary>
              Computes the log-likelihood that the given input vectors
              belongs to each class specified in <paramref name="classIndex"/>.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="classIndex">The class indices associated with each
              input vector whose scores will be computed.</param>
            <param name="result">An array where the distances will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihoods(`0)">
            <summary>
              Computes the log-likelihood that the given input 
              vector belongs to each of the possible classes.
            </summary>
            
            <param name="input">The input vector.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihoods(`0,System.Double[])">
            <summary>
              Computes the log-likelihood that the given input 
              vector belongs to each of the possible classes.
            </summary>
            
            <param name="input">The input vector.</param>
            <param name="result">An array where the log-likelihoods will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihoods(`0[])">
            <summary>
              Computes the log-likelihoods that the given input 
              vectors belongs to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.LogLikelihoods(`0[],System.Double[][])">
            <summary>
              Computes the log-likelihoods that the given input 
              vectors belongs to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="result">An array where the log-likelihoods will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probabilities(`0)">
            <summary>
              Computes the probabilities that the given input 
              vector belongs to each of the possible classes.
            </summary>
            
            <param name="input">The input vector.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probabilities(`0,System.Double[])">
            <summary>
              Computes the probabilities that the given input 
              vector belongs to each of the possible classes.
            </summary>
            
            <param name="input">The input vector.</param>
            <param name="result">An array where the probabilities will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probabilities(`0[])">
            <summary>
              Computes the probabilities that the given input 
              vectors belongs to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.Probabilities(`0[],System.Double[][])">
            <summary>
              Computes the probabilities that the given input 
              vectors belongs to each of the possible classes.
            </summary>
            
            <param name="input">A set of input vectors.</param>
            <param name="result">An array where the probabilities will be stored,
              avoiding unnecessary memory allocations.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.ToMulticlass">
            <summary>
            Views this instance as a multi-class generative classifier.
            </summary>
            
            <returns>
            This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1" />.
            </returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultilabelLikelihoodClassifier`1.ToMulticlass``1">
            <summary>
            Views this instance as a multi-class generative classifier.
            </summary>
            
            <returns>
            This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1" />.
            </returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IBinaryClassifier`1">
            <summary>
              Common interface for classification models. Classification models
              learn how to produce a class-label (or a set of class labels) <c>y</c>
              from an input vector <c>x</c>.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IBinaryClassifier`1.ToMulticlass">
            <summary>
              Views this instance as a multi-class classifier,
              giving access to more advanced methods, such as the prediction
              of integer labels.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassClassifier`1"/>.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IBinaryClassifier`1.ToMulticlass``1">
            <summary>
              Views this instance as a multi-class classifier,
              giving access to more advanced methods, such as the prediction
              of integer labels.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassClassifier`1"/>.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IBinaryClassifier">
            <summary>
              Common interface for classification models. Classification models
              learn how to produce a class-label (or a set of class labels) <c>y</c>
              from an input vector <c>x</c>.
            </summary>
            
        </member>
        <member name="T:Accord.MachineLearning.IBinaryScoreClassifier`1">
            <summary>
              Common interface for score-based binary classifiers. A binary classifier can
              predict whether or not an instance belongs to a class based on a decision score
              (a real number) that measures the association of the input with the negative
              and positive class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IBinaryScoreClassifier`1.ToMulticlass">
            <summary>
              Views this instance as a multi-class distance classifier,
              giving access to more advanced methods, such as the prediction
              of integer labels.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassScoreClassifier`1"/>.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IBinaryScoreClassifier`1.ToMulticlass``1">
            <summary>
              Views this instance as a multi-class distance classifier,
              giving access to more advanced methods, such as the prediction
              of integer labels.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassScoreClassifier`1"/>.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IBinaryLikelihoodClassifier`1">
            <summary>
              Common interface for generative binary classifiers. A binary classifier can
              predict whether or not an instance belongs to a class, while at the same time
              being able to provide the probability of this sample belonging to the positive
              class.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IBinaryLikelihoodClassifier`1.ToMulticlass">
            <summary>
              Views this instance as a multi-class generative classifier,
              giving access to more advanced methods, such as the prediction
              of integer labels.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1"/>.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.IBinaryLikelihoodClassifier`1.ToMulticlass``1">
            <summary>
              Views this instance as a multi-class generative classifier,
              giving access to more advanced methods, such as the prediction
              of integer labels.
            </summary>
            
            <returns>This instance seen as an <see cref="T:Accord.MachineLearning.IMulticlassLikelihoodClassifier`1"/>.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.ITagger`1">
            <summary>
              Common interface for generative observation sequence taggers. A sequence
              tagger can predict the class label of each individual observation in a 
              input sequence vector.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.ILikelihoodTagger`1">
            <summary>
              Common interface for generative observation sequence taggers. A sequence
              tagger can predict the class label of each individual observation in a 
              input sequence vector.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probability(`0[])">
            <summary>
              Predicts a the probability that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probability(`0[],System.Int32[]@)">
            <summary>
              Predicts a the probability that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probability(`0[][])">
            <summary>
              Predicts a the probability that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probability(`0[][],System.Double[])">
            <summary>
              Predicts a the probability that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probability(`0[][],System.Int32[][]@)">
            <summary>
              Predicts a the probability that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probability(`0[][],System.Int32[][]@,System.Double[])">
            <summary>
              Predicts a the probability that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihood(`0[])">
            <summary>
              Predicts a the probability that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihood(`0[],System.Int32[]@)">
            <summary>
              Predicts a the log-likelihood that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihood(`0[][])">
            <summary>
              Predicts a the log-likelihood that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihood(`0[][],System.Double[])">
            <summary>
              Predicts a the log-likelihood that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihood(`0[][],System.Int32[][]@)">
            <summary>
              Predicts a the log-likelihood that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihood(`0[][],System.Int32[][]@,System.Double[])">
            <summary>
              Predicts a the log-likelihood that the sequence vector 
              has been generated by this log-likelihood tagger.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probabilities(`0[])">
            <summary>
              Predicts a the probabilities for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probabilities(`0[],System.Double[][])">
            <summary>
              Predicts a the probabilities for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probabilities(`0[],System.Int32[]@)">
            <summary>
              Predicts a the probabilities for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probabilities(`0[],System.Int32[]@,System.Double[][])">
            <summary>
              Predicts a the probabilities for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihoods(`0[])">
            <summary>
              Predicts a the log-likelihood for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihoods(`0[],System.Double[][])">
            <summary>
              Predicts a the log-likelihood for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihoods(`0[],System.Int32[]@)">
            <summary>
              Predicts a the log-likelihood for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihoods(`0[],System.Int32[]@,System.Double[][])">
            <summary>
              Predicts a the log-likelihood for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probabilities(`0[][])">
            <summary>
              Predicts a the probabilities for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probabilities(`0[][],System.Double[][][])">
            <summary>
              Predicts a the probabilities for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probabilities(`0[][],System.Int32[][]@)">
            <summary>
              Predicts a the probabilities for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.Probabilities(`0[][],System.Int32[][]@,System.Double[][][])">
            <summary>
              Predicts a the probabilities for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihoods(`0[][])">
            <summary>
              Predicts a the log-likelihood for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihoods(`0[][],System.Double[][][])">
            <summary>
              Predicts a the log-likelihood for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihoods(`0[][],System.Int32[][]@)">
            <summary>
              Predicts a the log-likelihood for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.ILikelihoodTagger`1.LogLikelihoods(`0[][],System.Int32[][]@,System.Double[][][])">
            <summary>
              Predicts a the log-likelihood for each of the observations in 
              the sequence vector assuming each of the possible states in the
              tagger model.
            </summary>
            
        </member>
        <member name="T:Accord.MachineLearning.IScoreTagger`1">
            <summary>
            Common interface for observation sequence taggers.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IScoreTagger`1.Scores(`0[])">
            <summary>
              Computes numerical scores measuring the association between
              each of the given <paramref name="sequence"/> vectors and each
              possible class.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IScoreTagger`1.Scores(`0[],System.Double[][])">
            <summary>
              Computes numerical scores measuring the association between
              each of the given <paramref name="sequence"/> vectors and each
              possible class.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IScoreTagger`1.Scores(`0[],System.Int32[]@)">
            <summary>
              Computes numerical scores measuring the association between
              each of the given <paramref name="sequence"/> vectors and each
              possible class.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IScoreTagger`1.Scores(`0[],System.Int32[]@,System.Double[][])">
            <summary>
              Computes numerical scores measuring the association between
              each of the given <paramref name="sequence"/> vectors and each
              possible class.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IScoreTagger`1.Scores(`0[][])">
            <summary>
              Computes numerical scores measuring the association between
              each of the given <paramref name="sequences"/> vectors and each
              possible class.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IScoreTagger`1.Scores(`0[][],System.Double[][][])">
            <summary>
              Computes numerical scores measuring the association between
              each of the given <paramref name="sequences"/> vectors and each
              possible class.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IScoreTagger`1.Scores(`0[][],System.Int32[][]@)">
            <summary>
              Computes numerical scores measuring the association between
              each of the given <paramref name="sequences"/> vectors and each
              possible class.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IScoreTagger`1.Scores(`0[][],System.Int32[][]@,System.Double[][][])">
            <summary>
              Computes numerical scores measuring the association between
              each of the given <paramref name="sequences"/> vectors and each
              possible class.
            </summary>
            
        </member>
        <member name="T:Accord.MachineLearning.IParallel">
            <summary>
              Common interface for parallel algorithms.
            </summary>
            
        </member>
        <member name="P:Accord.MachineLearning.IParallel.ParallelOptions">
            <summary>
              Gets or sets the parallelization options for this algorithm. It can
              be used to control the maximum number of cores that should be used
              during the algorithm's execution.
            </summary>
            
            <remarks>
              The <see cref="T:Accord.MachineLearning.IParallel"/> interface is implemented by most machine learning
              algorithms in the framework, and it is most common use is to allow the user to
              tune how many cores should be used by a multi-threaded learning algorithm.
            </remarks>
            
            <example>
            <para>
              In the following example, we will be using the <see cref="P:Accord.MachineLearning.IParallel.ParallelOptions"/> property to limit 
              the maximum degree of parallelism of a support vector machine learning algorithm to be 1, meaning
              the algorithm will be running in a single thread:</para>
              <code source="Unit Tests\Accord.Tests.MachineLearning\VectorMachines\MulticlassSupportVectorLearningTest.cs" region="doc_learn_ldcd" />
            </example>
            
        </member>
        <member name="T:Accord.MachineLearning.ISupportsCancellation">
            <summary>
              Common interface for algorithms that can be canceled
              in the middle of execution.
            </summary>
            
        </member>
        <member name="P:Accord.MachineLearning.ISupportsCancellation.Token">
            <summary>
            Gets or sets a cancellation token that can be used
            to cancel the algorithm while it is running.
            </summary>
            
        </member>
        <member name="T:Accord.MachineLearning.ITransform">
            <summary>
              Common interface for data transformation algorithms. Examples of transformations include
              <see cref="T:Accord.MachineLearning.IClassifier`2">classifiers</see>, <see cref="T:Accord.MachineLearning.IRegression`2">
              regressions</see> and other machine learning techniques.
            </summary>
            
        </member>
        <member name="P:Accord.MachineLearning.ITransform.NumberOfInputs">
            <summary>
              Gets or sets the number of inputs accepted by the model.
            </summary>
            
        </member>
        <member name="P:Accord.MachineLearning.ITransform.NumberOfOutputs">
            <summary>
              Gets or sets the number of outputs generated by the model.
            </summary>
            
        </member>
        <member name="T:Accord.MachineLearning.ICovariantTransform`2">
            <summary>
              Common interface for data transformation algorithms. Examples of transformations include
              <see cref="T:Accord.MachineLearning.IClassifier`2">classifiers</see>, <see cref="T:Accord.MachineLearning.IRegression`2">regressions</see>
              and other machine learning techniques.
            </summary>
            
            <typeparam name="TInput">The type for the output data that enters in the model. Default is double[].</typeparam>
            <typeparam name="TOutput">The type for the input data that exits from the model. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.ICovariantTransform`2.Transform(`0)">
            <summary>
              Applies the transformation to an input, producing an associated output.
            </summary>
            
            <param name="input">The input data to which the transformation should be applied.</param>
            
            <returns>The output generated by applying this transformation to the given input.</returns>
            
        </member>
        <member name="M:Accord.MachineLearning.ICovariantTransform`2.Transform(`0[])">
            <summary>
              Applies the transformation to a set of input vectors,
              producing an associated set of output vectors.
            </summary>
            
            <param name="input">The input data to which
              the transformation should be applied.</param>
            
            <returns>The output generated by applying this
              transformation to the given input.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.ITransform`2">
            <summary>
              Common interface for data transformation algorithms. Examples of transformations include
              <see cref="T:Accord.MachineLearning.IClassifier`2">classifiers</see>, <see cref="T:Accord.MachineLearning.IRegression`2">regressions</see>
              and other machine learning techniques.
            </summary>
            
            <typeparam name="TInput">The type for the output data that enters in the model. Default is double[].</typeparam>
            <typeparam name="TOutput">The type for the input data that exits from the model. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.ITransform`2.Transform(`0[],`1[])">
            <summary>
              Applies the transformation to a set of input vectors,
              producing an associated set of output vectors.
            </summary>
            
            <param name="input">The input data to which
              the transformation should be applied.</param>
            <param name="result">The location to where to store the
              result of this transformation.</param>
            
            <returns>The output generated by applying this
              transformation to the given input.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultipleTransform`2">
            <summary>
              Common interface for data transformation algorithms. Examples of transformations include
              <see cref="T:Accord.MachineLearning.IClassifier`2">classifiers</see>, <see cref="T:Accord.MachineLearning.IRegression`2">regressions</see>
              and other machine learning techniques.
            </summary>
            
            <typeparam name="TInput">The type for the output data that enters in the model. Default is double[].</typeparam>
            <typeparam name="TOutput">The type for the input data that exits from the model. Default is double[].</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IMultipleTransform`2.Transform(`0[],`1)">
            <summary>
              Applies the transformation to a set of input vectors,
              producing an associated set of output vectors.
            </summary>
            
            <param name="input">The input data to which
              the transformation should be applied.</param>
            <param name="result">The location to where to store the
              result of this transformation.</param>
            
            <returns>The output generated by applying this
              transformation to the given input.</returns>
            
        </member>
        <member name="T:Accord.MachineLearning.ITransform`1">
            <summary>
              Common interface for data transformation algorithms. Examples of transformations include
              <see cref="T:Accord.MachineLearning.IClassifier`2">classifiers</see>, <see cref="T:Accord.MachineLearning.IRegression`2">regressions</see>
              and other machine learning techniques.
            </summary>
            
            <typeparam name="TInput">The type for the output data that enters in the model.</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultipleRegression`2">
            <summary>
              Common interface for multiple regression models. Multiple regression
              models learn how to produce a set of real values (a real-valued vector)
              from an input vector <c>x</c>.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TOutput">The data type for the predicted variables. Default is double.</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IMultipleRegression`1">
            <summary>
              Common interface for multiple regression models. Multiple regression
              models learn how to produce a set of real values (a real-valued vector)
              from an input vector <c>x</c>.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IRegression`2">
            <summary>
              Common interface for regression models. Regression models
              learn how to produce a real value (or a set of real values) <c>y</c>
              from an input vector <c>x</c>.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            <typeparam name="TOutput">The data type for the predicted variables. Default is double.</typeparam>
            
        </member>
        <member name="M:Accord.MachineLearning.IRegression`2.Regress(`0)">
            <summary>
              Computes an output value for a given <paramref name="input"/>.
            </summary>
            
            <param name="input">The input vector whose associated output
              value should be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IRegression`2.Regress(`0[])">
            <summary>
              Computes output values for each vector in the given set of
              <paramref name="input"/> vectors.
            </summary>
            
            <param name="input">The input vectors whose output values 
              should be computed.</param>
            
        </member>
        <member name="M:Accord.MachineLearning.IRegression`2.Regress(`0[],`1[])">
            <summary>
              Computes output values for each vector in the given set of
              <paramref name="input"/> vectors.
            </summary>
            
            <param name="input">The input vectors whose output values 
              should be computed.</param>
            <param name="result">The location where to store the output values.</param>
            
        </member>
        <member name="T:Accord.MachineLearning.IRegression`1">
            <summary>
              Common interface for regression models. Regression models
              learn how to produce a real value (or a set of real values) <c>y</c>
              from an input vector <c>x</c>.
            </summary>
            
            <typeparam name="TInput">The data type for the input data. Default is double[].</typeparam>
            
        </member>
        <member name="T:Accord.MachineLearning.IGenerative`1">
            <summary>
              Common interface for generative models.
            </summary>
            
        </member>
        <member name="M:Accord.MachineLearning.IGenerative`1.LogLikelihood(`0)">
            <summary>
            Predicts the probability that the input sample
            has been generated by this generative model.
            </summary>
            
        </member>
        <member name="T:Accord.Math.Converters.RationalConverter">
            <summary>
            Converts to and from the <see cref="T:Accord.Math.Rational"/> type.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Converters.RationalConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert an object of the given type to the type of this converter, using the specified context.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
            <param name="sourceType">A <see cref="T:System.Type" /> that represents the type you want to convert from.</param>
            <returns>true if this converter can perform the conversion; otherwise, false.</returns>
        </member>
        <member name="M:Accord.Math.Converters.RationalConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to use as the current culture.</param>
            <param name="value">The <see cref="T:System.Object" /> to convert.</param>
            <returns>An <see cref="T:System.Object" /> that represents the converted value.</returns>
            <exception cref="T:System.ArgumentException">Inavlid value type: " + value.GetType().FullName - value</exception>
        </member>
        <member name="M:Accord.Math.Converters.RationalConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert the object to the specified type, using the specified context.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
            <param name="destinationType">A <see cref="T:System.Type" /> that represents the type you want to convert to.</param>
            <returns>true if this converter can perform the conversion; otherwise, false.</returns>
        </member>
        <member name="M:Accord.Math.Converters.RationalConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Converts the given value object to the specified type, using the specified context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
            <param name="culture">A <see cref="T:System.Globalization.CultureInfo" />. If null is passed, the current culture is assumed.</param>
            <param name="value">The <see cref="T:System.Object" /> to convert.</param>
            <param name="destinationType">The <see cref="T:System.Type" /> to convert the <paramref name="value" /> parameter to.</param>
            <returns>An <see cref="T:System.Object" /> that represents the converted value.</returns>
            <exception cref="T:System.InvalidCastException">Cannot convert null to type " + destinationType.FullName</exception>
            <exception cref="T:System.ArgumentException">
            value must be a rational. - value
            or
            Inavlid destinationType: " + destinationType.FullName - destinationType
            </exception>
        </member>
        <member name="T:Accord.Math.Rational">
            <summary>
              Rational number.
            </summary>
            
        </member>
        <member name="F:Accord.Math.Rational.Zero">
            <summary>
            Represents the number zero.
            </summary>
        </member>
        <member name="F:Accord.Math.Rational.One">
            <summary>
            Represents the number one.
            </summary>
        </member>
        <member name="F:Accord.Math.Rational.MinValue">
            <summary>
            Represents the minimum finite value of a <see cref="T:Accord.Math.Rational"/>.
            </summary>
        </member>
        <member name="F:Accord.Math.Rational.MaxValue">
            <summary>
            Represents the maximum finite value of a <see cref="T:Accord.Math.Rational"/>.
            </summary>
        </member>
        <member name="F:Accord.Math.Rational.Indeterminate">
            <summary>
            Represents an indeterminate value.
            </summary>
        </member>
        <member name="F:Accord.Math.Rational.PositiveInfinity">
            <summary>
            Represents positive infinity.
            </summary>
        </member>
        <member name="F:Accord.Math.Rational.NegativeInfinity">
            <summary>
            Represents negative infinity.
            </summary>
        </member>
        <member name="F:Accord.Math.Rational.Epsilon">
            <summary>
            Represents the minimum positive value of a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <remarks>
            <para>
            This field has a value of 1 / 2,147,483,647.
            </para>
            <para>
            This does NOT represent the minimum possible difference between two <see cref="T:Accord.Math.Rational"/> instances; some rationals may have a smaller difference.
            If you try to subrtact two rationals whose difference is smaller than this value, you will get unexpected results due to overflow.
            </para>
            <example>
            To check for this case, you can add this value to one of the rationals and compare to the other rational.
            <code>
              if (r1 + Rational.Epsilon &gt; r2 &amp;&amp; r1 - Rational.Epsilon &lt; r2)
              {
                // Difference between r1 and r2 is less than Rational.Epsilon.
              }
            </code>
            </example>
            </remarks>
        </member>
        <member name="M:Accord.Math.Rational.Parse(System.String)">
            <summary>
            Converts the string representation of a number to its <see cref="T:Accord.Math.Rational"/> representation.
            </summary>
            <param name="s">A string that represents a number.</param>
            <returns>The parsed <see cref="T:Accord.Math.Rational"/> value.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Parse(System.String,System.Globalization.NumberStyles)">
            <summary>
            Converts the string representation of a number to its <see cref="T:Accord.Math.Rational"/> representation.
            </summary>
            <param name="s">A string that represents a number.</param>
            <param name="style">Indicates the styles that can be present when parsing a number.</param>
            <returns>The parsed <see cref="T:Accord.Math.Rational"/> value.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Parse(System.String,System.IFormatProvider)">
            <summary>
            Converts the string representation of a number to its <see cref="T:Accord.Math.Rational"/> representation.
            </summary>
            <param name="s">A string that represents a number.</param>
            <param name="provider">An object that supplies culture-specific information about the format of <paramref name="s"/>.</param>
            <returns>The parsed <see cref="T:Accord.Math.Rational"/> value.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Parse(System.String,System.Globalization.NumberStyles,System.IFormatProvider)">
            <summary>
            Converts the string representation of a number to its <see cref="T:Accord.Math.Rational"/> representation.
            </summary>
            <param name="s">A string that represents a number.</param>
            <param name="style">Indicates the styles that can be present when parsing a number.</param>
            <param name="provider">An object that supplies culture-specific information about the format of <paramref name="s"/>.</param>
            <returns>The parsed <see cref="T:Accord.Math.Rational"/> value.</returns>
        </member>
        <member name="M:Accord.Math.Rational.TryParse(System.String,Accord.Math.Rational@)">
            <summary>
            Converts the string representation of a number to its <see cref="T:Accord.Math.Rational"/> representation. A return value indicates whether the conversion succeeded or failed.
            </summary>
            <param name="s">A string that represents a number.</param>
            <param name="result">When this method returns, contains the parsed <see cref="T:Accord.Math.Rational"/> value.</param>
            <returns>True if the conversion succeeded; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.TryParse(System.String,System.Globalization.NumberStyles,Accord.Math.Rational@)">
            <summary>
            Converts the string representation of a number to its <see cref="T:Accord.Math.Rational"/> representation. A return value indicates whether the conversion succeeded or failed.
            </summary>
            <param name="s">A string that represents a number.</param>
            <param name="style">Indicates the styles that can be present when parsing a number.</param>
            <param name="result">When this method returns, contains the parsed <see cref="T:Accord.Math.Rational"/> value.</param>
            <returns>True if the conversion succeeded; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.TryParse(System.String,System.IFormatProvider,Accord.Math.Rational@)">
            <summary>
            Converts the string representation of a number to its <see cref="T:Accord.Math.Rational"/> representation. A return value indicates whether the conversion succeeded or failed.
            </summary>
            <param name="s">A string that represents a number.</param>
            <param name="provider">An object that supplies culture-specific information about the format of <paramref name="s"/>.</param>
            <param name="result">When this method returns, contains the parsed <see cref="T:Accord.Math.Rational"/> value.</param>
            <returns>True if the conversion succeeded; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.TryParse(System.String,System.Globalization.NumberStyles,System.IFormatProvider,Accord.Math.Rational@)">
            <summary>
            Converts the string representation of a number to its <see cref="T:Accord.Math.Rational"/> representation. A return value indicates whether the conversion succeeded or failed.
            </summary>
            <param name="s">A string that represents a number.</param>
            <param name="style">Indicates the styles that can be present when parsing a number.</param>
            <param name="provider">An object that supplies culture-specific information about the format of <paramref name="s"/>.</param>
            <param name="result">When this method returns, contains the parsed <see cref="T:Accord.Math.Rational"/> value.</param>
            <returns>True if the conversion succeeded; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.FromDouble(System.Double,System.Double)">
            <summary>
            Converts a floating-point number to a rational number.
            </summary>
            <param name="value">A floating-point number to convert to a rational number.</param>
            <param name="tolerance">The maximum error allowed between the result and the input value.</param>
            <returns>A rational number.</returns>
        </member>
        <member name="M:Accord.Math.Rational.FromDecimal(System.Decimal,System.Decimal)">
            <summary>
            Converts a floating-point number to a rational number.
            </summary>
            <param name="value">A floating-point number to convert to a rational number.</param>
            <param name="tolerance">The maximum error allowed between the result and the input value.</param>
            <returns>A rational number.</returns>
        </member>
        <member name="M:Accord.Math.Rational.FromDoubleWithMaxDenominator(System.Double,System.Int32,System.Double)">
            <summary>
            Converts a floating-point decimal to a rational number.
            </summary>
            <param name="value">A floating-point number to convert to a rational number.</param>
            <param name="maxDenominator">The maximum value that the denominator can have.</param>
            <param name="tolerance">
            The desired maximum error between the result and the input value. This is only used as an alternative end condition; 
            the actual error may be greater because of the limitation on the denominator value.
            </param>
            <returns>A rational number.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Abs(Accord.Math.Rational)">
            <summary>
            Returns the absolute value of a Rational.
            </summary>
            <param name="value">A Rational number.</param>
            <returns>Gets the absolute value of the Rational.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Min(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Returns the smaller of two Rationals.
            </summary>
            <param name="val1">A Rational number.</param>
            <param name="val2">A Rational number.</param>
            <returns>The smaller of two Rationals.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Max(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Returns the larger of two Rationals.
            </summary>
            <param name="val1">A Rational number.</param>
            <param name="val2">A Rational number.</param>
            <returns>The larger of two Rationals.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Round(Accord.Math.Rational)">
            <summary>
            Rounds a <see cref="T:Accord.Math.Rational"/> to the nearest integral value.
            </summary>
            <param name="x">The number to round.</param>
            <returns>The rounded number.</returns>
            <exception cref="T:System.DivideByZeroException">The denominator of <paramref name="x"/> is zero.</exception>
        </member>
        <member name="M:Accord.Math.Rational.Pow(Accord.Math.Rational,System.Int32)">
            <summary>
            Returns a specified number raised to a specified power.
            </summary>
            <param name="baseValue">A Rational number.</param>
            <param name="exponent">A Rational number.</param>
            <returns>The larger of two Rationals.</returns>
        </member>
        <member name="M:Accord.Math.Rational.DivRem(Accord.Math.Rational,Accord.Math.Rational,Accord.Math.Rational@)">
            <summary>
            Calculates the quotient of two rational numbers and also returns the remainder in an output parameter.
            </summary>
            <param name="a">The dividend.</param>
            <param name="b">The divisor.</param>
            <param name="remainder">The remainder.</param>
            <returns>The quotient.</returns>
        </member>
        <member name="M:Accord.Math.Rational.IsInfinity(Accord.Math.Rational)">
            <summary>
            Returns a value that indicates whether the specified <see cref="T:Accord.Math.Rational"/> evaluates to positive or negative infinity.
            </summary>
            <param name="r">A rational number.</param>
            <returns>True if <paramref name="r"/> evaluates to positive or negative infinity; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.IsPositiveInfinity(Accord.Math.Rational)">
            <summary>
            Returns a value that indicates whether the specified <see cref="T:Accord.Math.Rational"/> evaluates to positive infinity.
            </summary>
            <param name="r">A rational number.</param>
            <returns>True if <paramref name="r"/> evaluates to positive infinity; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.IsNegativeInfinity(Accord.Math.Rational)">
            <summary>
            Returns a value that indicates whether the specified <see cref="T:Accord.Math.Rational"/> evaluates to negative infinity.
            </summary>
            <param name="r">A rational number.</param>
            <returns>True if <paramref name="r"/> evaluates to negative infinity; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.IsIndeterminate(Accord.Math.Rational)">
            <summary>
            Returns a value that indicates whether the specified <see cref="T:Accord.Math.Rational"/> represents an indeterminate value.
            </summary>
            <param name="r">A rational number.</param>
            <returns>True if <paramref name="r"/> represents an indeterminate value; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.IsZero(Accord.Math.Rational)">
            <summary>
            Returns a value that indicates whether the specified <see cref="T:Accord.Math.Rational"/> evaluates to zero.
            </summary>
            <param name="r">A rational number.</param>
            <returns>True if <paramref name="r"/> evaluates to zero; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.#ctor(System.Int32)">
            <summary>
            Creates a new <see cref="T:Accord.Math.Rational"/> instance with a denominator of 1.
            </summary>
            <param name="numerator">The numerator of the <see cref="T:Accord.Math.Rational"/>.</param>
        </member>
        <member name="M:Accord.Math.Rational.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates a new <see cref="T:Accord.Math.Rational"/> instance.
            </summary>
            <param name="numerator">The numerator of the <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="denominator">The denominator of the <see cref="T:Accord.Math.Rational"/>.</param>
        </member>
        <member name="M:Accord.Math.Rational.#ctor(System.Double)">
            <summary>
            Creates a new <see cref="T:Accord.Math.Rational"/> instance equal to a floating-point value.
            </summary>
            <param name="value">A floating-point value</param>
        </member>
        <member name="P:Accord.Math.Rational.Numerator">
            <summary>
            Gets the numerator of the current <see cref="T:Accord.Math.Rational"/> value.
            </summary>
        </member>
        <member name="P:Accord.Math.Rational.Denominator">
            <summary>
            Gets the numerator of the current <see cref="T:Accord.Math.Rational"/> value.
            </summary>
        </member>
        <member name="P:Accord.Math.Rational.Value">
            <summary>
            Gets the floating-point equivalent of the current <see cref="T:Accord.Math.Rational"/> value.
            </summary>
        </member>
        <member name="M:Accord.Math.Rational.Inverse">
            <summary>
            Gets the inverse of the current <see cref="T:Accord.Math.Rational"/> value (that is, one divided by the current value).
            </summary>
            <returns></returns>
        </member>
        <member name="M:Accord.Math.Rational.Negate">
            <summary>
            Gets the negated version of the current <see cref="T:Accord.Math.Rational"/> value (that is, the current value multiplied by negative one).
            </summary>
            <returns></returns>
        </member>
        <member name="M:Accord.Math.Rational.Simplify(System.Int32,System.Int32)">
            <summary>
            Gets the simplified version of the rational number.
            </summary>
        </member>
        <member name="M:Accord.Math.Rational.Simplify(System.Int64,System.Int64)">
            <summary>
            Gets the simplified version of the rational number.
            </summary>
        </member>
        <member name="M:Accord.Math.Rational.Simplify">
            <summary>
            Gets the simplified version of the rational number.
            </summary>
        </member>
        <member name="M:Accord.Math.Rational.ToDouble">
            <summary>
            Converts the current <see cref="T:Accord.Math.Rational"/> to a double.
            </summary>
            <returns>A <see cref="T:System.Double"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.ToString">
            <summary>
            Converts the Rational to a string in the form of an improper fraction.
            </summary>
            <returns>A string representaion of the rational number.</returns>
        </member>
        <member name="M:Accord.Math.Rational.ToString(System.IFormatProvider)">
            <summary>
            Converts this instance to its equivalent string representation.
            </summary>
            <param name="provider">An object that has culture-specific formatting information.</param>
            <returns>The string representation of the <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.ToMixedString">
            <summary>
            Converts the Rational to a string in the form of a mixed fraction.
            </summary>
            <returns>A string representaion of the rational number.</returns>
        </member>
        <member name="M:Accord.Math.Rational.ToMixedString(System.String)">
            <summary>
            Converts the Rational to a string in the form of a mixed fraction.
            </summary>
            <param name="numberSeparator">The separator between the number part and the fraction part</param>
            <returns>A string representaion of the rational number.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Equals(System.Object)">
            <summary>
            Indicates whether this instance and a specified object are equal.
            </summary>
            <param name="obj">Another object to compare to.</param>
            <returns><value>true</value> if the current object and <paramref name="obj"/> are the same type and represent the same value; otherwise, <value>false</value>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.StrictlyEquals(Accord.Math.Rational)">
            <summary>
            Indicates whether this instance and a specified <see cref="T:Accord.Math.Rational"/> are strictly equal; that is, the two instances must have equal numerators and denominators.
            </summary>
            <param name="r">Another <see cref="T:Accord.Math.Rational"/> to compare to.</param>
            <returns><value>true</value> if the current number and <paramref name="r"/> have equal numerators and denominators; otherwise, <value>false</value>.</returns>
            <remarks>
            The basic Equals implementation considers unsimplified fractions to be equal to their simplified forms; e.g. 2/4 = 1/2.
            This method considers those values to be different.
            </remarks>
        </member>
        <member name="M:Accord.Math.Rational.GetHashCode">
            <summary>
            Returns the hash code for this instance.
            </summary>
            <returns>the hash code.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Equality(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Gets whether two <see cref="T:Accord.Math.Rational"/> values are numerically equivalent.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>True if the values are equivalent; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Inequality(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Gets whether two <see cref="T:Accord.Math.Rational"/> values are numerically not equivalent.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>False if the values are equivalent; otherwise true.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_GreaterThan(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Gets whether a <see cref="T:Accord.Math.Rational"/> values is greater than another.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>True if <paramref name="x"/> is greater than <paramref name="y"/>; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_GreaterThanOrEqual(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Gets whether a <see cref="T:Accord.Math.Rational"/> values is greater than or equal to another.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>True if <paramref name="x"/> is greater than or equal to <paramref name="y"/>; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_LessThan(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Gets whether a <see cref="T:Accord.Math.Rational"/> values is less than another.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>True if <paramref name="x"/> is less than <paramref name="y"/>; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_LessThanOrEqual(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Gets whether a <see cref="T:Accord.Math.Rational"/> values is less than or equal to another.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>True if <paramref name="x"/> is less than or equal to <paramref name="y"/>; otherwise false.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_UnaryPlus(Accord.Math.Rational)">
            <summary>
            Gets the <see cref="T:Accord.Math.Rational"/> value.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_UnaryNegation(Accord.Math.Rational)">
            <summary>
            Gets the negated version of a <see cref="T:Accord.Math.Rational"/> value.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Addition(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Adds two <see cref="T:Accord.Math.Rational"/> values.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>The simplified sum of the <see cref="T:Accord.Math.Rational"/> values.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Subtraction(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Subtracts two <see cref="T:Accord.Math.Rational"/> values.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>The simplified difference of the <see cref="T:Accord.Math.Rational"/> values.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Multiply(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Multiplies two <see cref="T:Accord.Math.Rational"/> values.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>The simplified product of the <see cref="T:Accord.Math.Rational"/> values.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Division(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Divides two <see cref="T:Accord.Math.Rational"/> values.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>The simplified dividend of the <see cref="T:Accord.Math.Rational"/> values.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Modulus(Accord.Math.Rational,Accord.Math.Rational)">
            <summary>
            Gets the remainder that results from dividing two <see cref="T:Accord.Math.Rational"/> values.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <param name="y">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>The remainder that results from dividing the <see cref="T:Accord.Math.Rational"/> values.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Increment(Accord.Math.Rational)">
            <summary>
            Increments a <see cref="T:Accord.Math.Rational"/> value.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>The incremented <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Decrement(Accord.Math.Rational)">
            <summary>
            Decrements a <see cref="T:Accord.Math.Rational"/> value.
            </summary>
            <param name="x">A <see cref="T:Accord.Math.Rational"/>.</param>
            <returns>The idecremented <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Implicit(System.Int32)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.Int32"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.Int32"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.Int32">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.Int32"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.Int32"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Implicit(System.UInt32)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.UInt32"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.UInt32"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.UInt32">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.UInt32"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.UInt32"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Implicit(System.Int16)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.Int16"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.Int16"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.Int16">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.Int16"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.Int16"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Implicit(System.UInt16)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.UInt16"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.UInt16"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.UInt16">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.UInt16"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.UInt16"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Implicit(System.Int64)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.Int64"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.Int64"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.Int64">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.Int64"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.Int64"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Implicit(System.UInt64)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.UInt64"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.UInt64"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.UInt64">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.UInt64"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.UInt64"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Implicit(System.SByte)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.SByte"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.SByte"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.SByte">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.SByte"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.SByte"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Implicit(System.Byte)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.Byte"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.Byte"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.Byte">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.Byte"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.Byte"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(System.Single)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.Single"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.Single"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.Single">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.Single"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.Single"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(System.Double)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.Double"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.Double"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.Double">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.Double"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.Double"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(System.Decimal)~Accord.Math.Rational">
            <summary>
            Converts the specified <see cref="T:System.Decimal"/> to a <see cref="T:Accord.Math.Rational"/>.
            </summary>
            <param name="x">The <see cref="T:System.Decimal"/> to convert.</param>
            <returns>A <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.op_Explicit(Accord.Math.Rational)~System.Decimal">
            <summary>
            Converts the specified <see cref="T:Accord.Math.Rational"/> to a <see cref="T:System.Decimal"/>.
            </summary>
            <param name="x">The <see cref="T:Accord.Math.Rational"/> to convert.</param>
            <returns>A <see cref="T:System.Decimal"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.CompareTo(System.Object)">
            <summary>
            Compares this instance to another <see cref="T:Accord.Math.Rational"/> and returns an indication of their relative values.
            </summary>
            <param name="obj">The value to compare to. This should be a numeric value.</param>
            <returns>A signed number indicating the relative values of this instance and <paramref name="obj"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.CompareTo(Accord.Math.Rational)">
            <summary>
            Compares this instance to another <see cref="T:Accord.Math.Rational"/> and returns an indication of their relative values.
            </summary>
            <param name="other">The <see cref="T:Accord.Math.Rational"/> to compare to.</param>
            <returns>A signed number indicating the relative values of this instance and <paramref name="other"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.ToString(System.String,System.IFormatProvider)">
            <summary>
            Converts this instance to its equivalent string representation.
            </summary>
            <param name="format">The format to use for both the numerator and the denominator.</param>
            <param name="formatProvider">An object that has culture-specific formatting information.</param>
            <returns>The string representation of the <see cref="T:Accord.Math.Rational"/>.</returns>
        </member>
        <member name="M:Accord.Math.Rational.Equals(Accord.Math.Rational)">
            <summary>
            Indicates whether this instance and a specified <see cref="T:Accord.Math.Rational"/> are equal.
            </summary>
            <param name="other">Another <see cref="T:Accord.Math.Rational"/> to compare to.</param>
            <returns><value>true</value> if the current object and <paramref name="other"/> represent the same value; otherwise, <value>false</value>.</returns>
        </member>
        <member name="T:Accord.Math.Hyperrectangle">
            <summary>
              Hyperrectangle structure.
            </summary>
            
            <remarks>
              <para>
              In geometry, an n-orthotope (also called a hyperrectangle or a box) is the generalization of a rectangle for higher 
              dimensions, formally defined as the Cartesian product of intervals.</para>
              
            <para>
               References:
               <list type="bullet">
                 <item><description>
                   Wikipedia contributors, "Hyperrectangle," Wikipedia, The Free Encyclopedia, 
                   https://en.wikipedia.org/w/index.php?title=Hyperrectangle </description></item>
                </list></para>     
            </remarks>
            
            <seealso cref="T:System.IFormattable" />
            
        </member>
        <member name="P:Accord.Math.Hyperrectangle.Min">
            <summary>
            Gets the minimum point defining the lower bound of the hyperrectangle.
            </summary>
            
        </member>
        <member name="P:Accord.Math.Hyperrectangle.Max">
            <summary>
            Gets the maximum point defining the upper bound of the hyperrectangle.
            </summary>
            
        </member>
        <member name="P:Accord.Math.Hyperrectangle.NumberOfDimensions">
            <summary>
              Gets the number of dimensions of the hyperrectangle.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Hyperrectangle.GetLength">
            <summary>
              Gets the length of each dimension. The length of the first dimension
              can be referred as the width, the second as the height, and so on.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Hyperrectangle.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Math.Hyperrectangle"/> struct.
            </summary>
            
            <param name="x">The x-coordinate of the upper-left corner of the rectangle..</param>
            <param name="y">The y-coordinate of the upper-left corner of the rectangle.</param>
            <param name="width">The width of the rectangle.</param>
            <param name="height">The height of the rectangle.</param>
            
        </member>
        <member name="M:Accord.Math.Hyperrectangle.#ctor(System.Double[],System.Double[],System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Math.Hyperrectangle"/> struct.
            </summary>
            
            <param name="min">The minimum point in the hyperrectangle (the lower bound).</param>
            <param name="max">The maximum point in the hyperrectangle (the upper bound).</param>
            <param name="copy">Whether the passed vectors should be copied into this instance
              or used as-is. Default is true (elements from the given vectors will be copied
              into new array instances).</param>
            
        </member>
        <member name="M:Accord.Math.Hyperrectangle.FromMinAndMax(System.Double[],System.Double[],System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Math.Hyperrectangle"/> struct from minimum and maximum values.
            </summary>
            
            <param name="min">The minimum point in the hyperrectangle (the lower bound).</param>
            <param name="max">The maximum point in the hyperrectangle (the upper bound).</param>
            <param name="copy">Whether the passed vectors should be copied into this instance
              or used as-is. Default is true (elements from the given vectors will be copied
              into new array instances).</param>
            
        </member>
        <member name="M:Accord.Math.Hyperrectangle.FromMinAndLength(System.Double[],System.Double[],System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Math.Hyperrectangle"/> struct from a minimum value and a size.
            </summary>
            
            <param name="min">The minimum point in the hyperrectangle (the lower bound).</param>
            <param name="size">The size of each dimension (i.e. width, height, and so on).</param>
            <param name="copy">Whether the passed vectors should be copied into this instance
              or used as-is. Default is true (elements from the given vectors will be copied
              into new array instances).</param>
            
        </member>
        <member name="M:Accord.Math.Hyperrectangle.IntersectsWith(Accord.Math.Hyperrectangle)">
            <summary>
              Determines if this rectangle intersects with rect.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Hyperrectangle.Contains(System.Double[])">
            <summary>
              Determines if the specified point is contained within this Hyperrectangle structure.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Hyperrectangle.Equals(Accord.Math.Hyperrectangle)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:Accord.Math.Hyperrectangle.Clone">
            <summary>
            Creates a new object that is a copy of the current instance.
            </summary>
            <returns>A new object that is a copy of this instance.</returns>
        </member>
        <member name="M:Accord.Math.Hyperrectangle.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>A <see cref="T:System.String" /> that represents this instance.</returns>
        </member>
        <member name="M:Accord.Math.Hyperrectangle.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <param name="format">The format to use.-or- A null reference (Nothing in Visual Basic) to use the default format defined for the type of the <see cref="T:System.IFormattable" /> implementation.</param>
            <param name="formatProvider">The provider to use to format the value.-or- A null reference (Nothing in Visual Basic) to obtain the numeric format information from the current locale setting of the operating system.</param>
            <returns>A <see cref="T:System.String" /> that represents this instance.</returns>
        </member>
        <member name="T:Accord.Math.Sparse">
            <summary>
              Extension methods for <see cref="T:Accord.Math.Sparse`1">sparse vectors</see>.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse.Parse(System.String,System.Nullable{System.Double})">
             <summary>
               Parses a string containing a sparse array in LibSVM
               format into a <see cref="T:Accord.Math.Sparse`1"/> vector.
             </summary>
            
             <param name="values">An array of "index:value" strings indicating
               where each value belongs in the sparse vector.</param>
             <param name="insertValueAtBeginning">Whether an intercept term should be added
               at the beginning of the vector.</param>
            
        </member>
        <member name="M:Accord.Math.Sparse.Parse(System.String[],System.Nullable{System.Double})">
             <summary>
               Parses a string containing a sparse array in LibSVM
               format into a <see cref="T:Accord.Math.Sparse`1"/> vector.
             </summary>
            
             <param name="values">An array of "index:value" strings indicating
               where each value belongs in the sparse vector.</param>
             <param name="insertValueAtBeginning">Whether an intercept term should be added
               at the beginning of the vector.</param>
            
        </member>
        <member name="M:Accord.Math.Sparse.ToDense``1(Accord.Math.Sparse{``0}[])">
            <summary>
              Converts an array of sparse vectors into a jagged matrix.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse.ToDense``1(Accord.Math.Sparse{``0}[],System.Int32)">
            <summary>
              Converts an array of sparse vectors into a jagged matrix.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse.FromDense``1(``0[],System.Boolean)">
            <summary>
              Creates a sparse vector from a dense array.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse.FromDense``1(``0[][],System.Boolean)">
            <summary>
              Creates sparse vectors from dense arrays.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse.Columns``1(Accord.Math.Sparse{``0}[])">
            <summary>
              Gets the maximum number of columns (dimensions) 
              that can be inferred from the given sparse vectors.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse.FromDictionary(System.Collections.Generic.IDictionary{System.Int32,System.Int32})">
            <summary>
              Creates a sparse vector from a dictionary mapping indices to values.
            </summary>
            
        </member>
        <member name="T:Accord.Math.Sparse`1">
            <summary>
              Sparse vector representation (in LibSVM format).
            </summary>
            
            <typeparam name="T">The type for the non-zero elements in this vector.</typeparam>
            
        </member>
        <member name="P:Accord.Math.Sparse`1.Indices">
            <summary>
              Gets or sets the vector of indices indicating the location
              of the non-zero elements contained in this sparse vector.
            </summary>
            
        </member>
        <member name="P:Accord.Math.Sparse`1.Values">
            <summary>
              Gets or sets the vector of values indicating which non-zero
              value happens at each position indicated in <see cref="P:Accord.Math.Sparse`1.Indices"/>.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse`1.#ctor">
            <summary>
              Creates a sparse vector with zero elements.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse`1.#ctor(System.Int32)">
            <summary>
              Creates a sparse vector with the maximum number of elements.
            </summary>
            
            <param name="length">The maximum number of non-zero
              elements that this vector can accomodate.</param>
              
        </member>
        <member name="M:Accord.Math.Sparse`1.#ctor(System.Int32[],`0[])">
            <summary>
              Creates a sparse vector from a vector of indices
              and a vector of values occuring at those indices.
            </summary>
            
            <param name="indices">The indices for non-zero entries.</param>
            <param name="values">The non-zero values happening at each index.</param>
            
        </member>
        <member name="M:Accord.Math.Sparse`1.ToDense">
            <summary>
              Converts this sparse vector to a dense vector of the given length.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse`1.ToDense(System.Int32)">
            <summary>
              Converts this sparse vector to a dense vector of the given length.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse`1.ToSparse">
            <summary>
              Converts this sparse vector to a sparse representation where 
              the indices are intertwined with their corresponding values.
            </summary>
            
        </member>
        <member name="P:Accord.Math.Sparse`1.Item(System.Int32)">
            <summary>
              Gets the the value stored at position <paramref name="i"/>.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse`1.Clone">
            <summary>
              Creates a new object that is a copy of the current instance.
            </summary>
            
            <returns>
              A new object that is a copy of this instance.
            </returns>
            
        </member>
        <member name="M:Accord.Math.Sparse`1.op_Implicit(Accord.Math.Sparse{`0})~System.Array">
            <summary>
              Performs an implicit conversion from <see cref="T:Accord.Math.Sparse`1"/> to <see cref="T:System.Array"/>.
            </summary>
            
        </member>
        <member name="P:Accord.Math.Sparse`1.Length">
            <summary>
              Gets the maximum non-zero element index in the sparse vector.
            </summary>
            
        </member>
        <member name="M:Accord.Math.Sparse`1.CopyTo(`0[],System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.ICollection`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
        </member>
        <member name="M:Accord.Math.Sparse`1.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="M:Accord.Math.Sparse`1.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="M:Accord.Math.Sparse`1.IsFull">
            <summary>
            Determines whether this Sparse vector has elements on all indices.
            </summary>
            
            <returns><c>true</c> if this instance is full; otherwise, <c>false</c>.</returns>
            
        </member>
        <member name="T:Accord.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Accord.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Accord.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Accord.Properties.Resources.NotSupportedWeights">
            <summary>
              Looks up a localized string similar to This algorithm does not support instance weights at this time. Please feel free to report this as an enhancement request in the project issue tracker - or even submit a pull request implementing this feature!.
            </summary>
        </member>
        <member name="T:Accord.ByteRange">
            <summary>
              Represents a byte range with minimum and maximum values.
            </summary>
            
            <remarks>
              This class represents a byt range with inclusive limits, where
              both minimum and maximum values of the range are included into it.
              Mathematical notation of such range is <b>[min, max]</b>.
            </remarks>
            
            <example>
            <code>
            // create [0, 255] range
            var range1 = new ByteRange(0, 255);
            
            // create [64, 128] range
            var range2 = new ByteRange(64, 128);
            
            // check if values is inside of the first range
            if (range1.IsInside(5))
            {
                // ...
            }
            
            // check if the second range is inside of the first range
            if (range1.IsInside(range2))
            {
                // ...
            }
            
            // check if two ranges overlap
            if (range1.IsOverlapping(range2))
            {
                // ...
            }
            </code>
            </example>
            
            <seealso cref="T:Accord.IntRange"/>
            <seealso cref="T:Accord.DoubleRange"/>
            <seealso cref="T:Accord.Range"/>
            
        </member>
        <member name="P:Accord.ByteRange.Min">
            <summary>
              Minimum value of the range.
            </summary>
            
            <remarks>
              Represents minimum value (left side limit) of the range [<b>min</b>, max].
            </remarks>
            
        </member>
        <member name="P:Accord.ByteRange.Max">
            <summary>
              Maximum value of the range.
            </summary>
            
            <remarks>
              Represents maximum value (right side limit) of the range [min, <b>max</b>].
            </remarks>
            
        </member>
        <member name="P:Accord.ByteRange.Length">
            <summary>
              Gets the length of the range, defined as (max - min).
            </summary>
            
        </member>
        <member name="M:Accord.ByteRange.#ctor(System.Byte,System.Byte)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.IntRange"/> class.
            </summary>
            
            <param name="min">Minimum value of the range.</param>
            <param name="max">Maximum value of the range.</param>
            
        </member>
        <member name="M:Accord.ByteRange.IsInside(System.Byte)">
            <summary>
              Check if the specified value is inside of the range.
            </summary>
            
            <param name="x">Value to check.</param>
            
            <returns>
              <b>True</b> if the specified value is inside of the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.IsInside(Accord.ByteRange)">
            <summary>
              Check if the specified range is inside of the range.
            </summary>
            
            <param name="range">Range to check.</param>
            
            <returns>
              <b>True</b> if the specified range is inside of the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.IsOverlapping(Accord.ByteRange)">
            <summary>
              Check if the specified range overlaps with the range.
            </summary>
            
            <param name="range">Range to check for overlapping.</param>
            
            <returns>
              <b>True</b> if the specified range overlaps with the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.Intersection(Accord.ByteRange)">
            <summary>
              Computes the intersection between two ranges.
            </summary>
            
            <param name="range">The second range for which the intersection should be calculated.</param>
            
            <returns>An new <see cref="T:Accord.ByteRange"/> structure containing the intersection
            between this range and the <paramref name="range"/> given as argument.</returns>
            
        </member>
        <member name="M:Accord.ByteRange.op_Equality(Accord.ByteRange,Accord.ByteRange)">
            <summary>
              Determines whether two instances are equal.
            </summary>
            
        </member>
        <member name="M:Accord.ByteRange.op_Inequality(Accord.ByteRange,Accord.ByteRange)">
            <summary>
              Determines whether two instances are not equal.
            </summary>
            
        </member>
        <member name="M:Accord.ByteRange.Equals(Accord.ByteRange)">
            <summary>
              Indicates whether the current object is equal to another object of the same type.
            </summary>
            
            <param name="other">An object to compare with this object.</param>
            
            <returns>
              true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.Equals(System.Object)">
            <summary>
              Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
            
            <param name="obj">The <see cref="T:System.Object" /> to compare with this instance.</param>
            
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.GetHashCode">
            <summary>
              Returns a hash code for this instance.
            </summary>
            
            <returns>
              A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.ToString">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.ToString(System.String,System.IFormatProvider)">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.op_Implicit(Accord.ByteRange)~Accord.IntRange">
            <summary>
              Performs an implicit conversion from <see cref="T:Accord.ByteRange"/> to <see cref="T:Accord.IntRange"/>.
            </summary>
            
            <param name="range">The range.</param>
            
            <returns>
              The result of the conversion.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.op_Implicit(Accord.ByteRange)~Accord.DoubleRange">
            <summary>
              Performs an implicit conversion from <see cref="T:Accord.IntRange"/> to <see cref="T:Accord.DoubleRange"/>.
            </summary>
            
            <param name="range">The range.</param>
            
            <returns>
              The result of the conversion.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.op_Implicit(Accord.ByteRange)~Accord.Range">
            <summary>
              Performs an implicit conversion from <see cref="T:Accord.IntRange"/> to <see cref="T:Accord.Range"/>.
            </summary>
            
            <param name="range">The range.</param>
            
            <returns>
              The result of the conversion.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through a collection.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.ByteRange.System#Collections#IEnumerable#GetEnumerator">
            <summary>
              Returns an enumerator that iterates through a collection.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="T:Accord.DoubleRange">
            <summary>
              Represents a double range with minimum and maximum values.
            </summary>
            
            <remarks>
              This class represents a double range with inclusive limits, where
              both minimum and maximum values of the range are included into it.
              Mathematical notation of such range is <b>[min, max]</b>.
            </remarks>
            
            <example>
            <code>
            // create [0.25, 1.5] range
            var range1 = new DoubleRange(0.25, 1.5);
            
            // create [1.00, 2.25] range
            var range2 = new DoubleRange(1.00, 2.25);
            
            // check if values is inside of the first range
            if (range1.IsInside(0.75))
            {
                // ...
            }
            
            // check if the second range is inside of the first range
            if (range1.IsInside(range2))
            {
                // ...
            }
            
            // check if two ranges overlap
            if (range1.IsOverlapping(range2))
            {
                // ...
            }
            </code>
            </example>
            
            <seealso cref="T:Accord.ByteRange"/>
            <seealso cref="T:Accord.IntRange"/>
            <seealso cref="T:Accord.Range"/>
            
        </member>
        <member name="P:Accord.DoubleRange.Min">
            <summary>
              Minimum value of the range.
            </summary>
            
            <remarks>
              Represents minimum value (left side limit) of the range [<b>min</b>, max].
            </remarks>
            
        </member>
        <member name="P:Accord.DoubleRange.Max">
            <summary>
              Maximum value of the range.
            </summary>
            
            <remarks>
              Represents maximum value (right side limit) of the range [min, <b>max</b>].
            </remarks>
            
        </member>
        <member name="P:Accord.DoubleRange.Length">
            <summary>
              Gets the length of the range, defined as (max - min).
            </summary>
            
        </member>
        <member name="M:Accord.DoubleRange.#ctor(System.Double,System.Double)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.DoubleRange"/> class.
            </summary>
            
            <param name="min">Minimum value of the range.</param>
            <param name="max">Maximum value of the range.</param>
            
        </member>
        <member name="M:Accord.DoubleRange.IsInside(System.Double)">
            <summary>
              Check if the specified value is inside of the range.
            </summary>
            
            <param name="x">Value to check.</param>
            
            <returns>
              <b>True</b> if the specified value is inside of the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.DoubleRange.IsInside(Accord.DoubleRange)">
            <summary>
              Check if the specified range is inside of the range.
            </summary>
            
            <param name="range">Range to check.</param>
            
            <returns>
              <b>True</b> if the specified range is inside of the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.DoubleRange.IsOverlapping(Accord.DoubleRange)">
            <summary>
              Check if the specified range overlaps with the range.
            </summary>
            
            <param name="range">Range to check for overlapping.</param>
            
            <returns>
              <b>True</b> if the specified range overlaps with the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.DoubleRange.Intersection(Accord.DoubleRange)">
            <summary>
              Computes the intersection between two ranges.
            </summary>
            
            <param name="range">The second range for which the intersection should be calculated.</param>
            
            <returns>An new <see cref="T:Accord.IntRange"/> structure containing the intersection
            between this range and the <paramref name="range"/> given as argument.</returns>
            
        </member>
        <member name="M:Accord.DoubleRange.op_Equality(Accord.DoubleRange,Accord.DoubleRange)">
            <summary>
              Determines whether two instances are equal.
            </summary>
            
        </member>
        <member name="M:Accord.DoubleRange.op_Inequality(Accord.DoubleRange,Accord.DoubleRange)">
            <summary>
              Determines whether two instances are not equal.
            </summary>
            
        </member>
        <member name="M:Accord.DoubleRange.Equals(Accord.DoubleRange)">
            <summary>
              Indicates whether the current object is equal to another object of the same type.
            </summary>
            
            <param name="other">An object to compare with this object.</param>
            
            <returns>
              true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.DoubleRange.Equals(System.Object)">
            <summary>
              Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
            
            <param name="obj">The <see cref="T:System.Object" /> to compare with this instance.</param>
            
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            
        </member>
        <member name="M:Accord.DoubleRange.GetHashCode">
            <summary>
              Returns a hash code for this instance.
            </summary>
            
            <returns>
              A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
            
        </member>
        <member name="M:Accord.DoubleRange.ToString">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="M:Accord.DoubleRange.ToString(System.String,System.IFormatProvider)">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="M:Accord.DoubleRange.ToIntRange(System.Boolean)">
             <summary>
               Converts this double-precision range into an <see cref="T:Accord.IntRange"/>.
             </summary>
             
             <param name="provideInnerRange">
               Specifies if inner integer range must be returned or outer range.</param>
             
             <returns>Returns integer version of the range.</returns>
             
             <remarks>
               If <paramref name="provideInnerRange"/> is set to <see langword="true"/>, then the
               returned integer range will always fit inside of the current single precision range.
               If it is set to <see langword="false"/>, then current single precision range will always
               fit into the returned integer range.
             </remarks>
            
        </member>
        <member name="T:Accord.CommunicationBufferEventArgs">
            <summary>
            Event arguments holding a buffer sent or received during some communication process.
            </summary>
        </member>
        <member name="P:Accord.CommunicationBufferEventArgs.MessageLength">
            <summary>
            Length of the transfered message.
            </summary>
        </member>
        <member name="M:Accord.CommunicationBufferEventArgs.#ctor(System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.CommunicationBufferEventArgs"/> class.
            </summary>
            
            <param name="message">Message being transfered during communication process.</param>
            
        </member>
        <member name="M:Accord.CommunicationBufferEventArgs.#ctor(System.Byte[],System.Int32,System.Int32)">
             <summary>
             Initializes a new instance of the <see cref="T:Accord.CommunicationBufferEventArgs"/> class.
             </summary>
            
             <param name="buffer">Buffer containing the message being transferred during communication process.</param>
             <param name="index">Starting index of the message within the buffer.</param>
             <param name="length">Length of the message within the buffer.</param>
            
        </member>
        <member name="M:Accord.CommunicationBufferEventArgs.GetMessage">
            <summary>
            Get the transfered message.
            </summary>
            
            <returns>Returns copy of the transfered message.</returns>
            
        </member>
        <member name="M:Accord.CommunicationBufferEventArgs.GetMessageString">
             <summary>
             Get the transferred message as string.
             </summary>
             
             <returns>Returns string encoding the transferred message.</returns>
            
        </member>
        <member name="T:Accord.ConnectionFailedException">
            <summary>
            Connection failed exception.
            </summary>
            
            <remarks><para>The exception is thrown in the case if connection to device
            has failed.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.ConnectionFailedException.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConnectionFailedException"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.ConnectionFailedException.#ctor(System.String)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConnectionFailedException"/> class.
            </summary>
            
            <param name="message">Exception's message.</param>
            
        </member>
        <member name="M:Accord.ConnectionFailedException.#ctor(System.String,System.Exception)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConnectionFailedException"/> class.
            </summary>
            
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
            
        </member>
        <member name="T:Accord.ConnectionLostException">
            <summary>
            Connection lost exception.
            </summary>
            
            <remarks><para>The exception is thrown in the case if connection to device
            is lost. When the exception is caught, user may need to reconnect to the device.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.ConnectionLostException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.ConnectionLostException"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.ConnectionLostException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.ConnectionLostException"/> class.
            </summary>
            
            <param name="message">Exception's message.</param>
            
        </member>
        <member name="M:Accord.ConnectionLostException.#ctor(System.String,System.Exception)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConnectionLostException"/> class.
            </summary>
            
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
            
        </member>
        <member name="M:Accord.ConnectionLostException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConnectionLostException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
            
        </member>
        <member name="T:Accord.NotConnectedException">
            <summary>
            Not connected exception.
            </summary>
            
            <remarks><para>The exception is thrown in the case if connection to device
            is not established, but user requests for its services.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.NotConnectedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NotConnectedException"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.NotConnectedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NotConnectedException"/> class.
            </summary>
            
            <param name="message">Exception's message.</param>
            
        </member>
        <member name="M:Accord.NotConnectedException.#ctor(System.String,System.Exception)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.NotConnectedException"/> class.
            </summary>
            
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
            
        </member>
        <member name="M:Accord.NotConnectedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.NotConnectedException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
            
        </member>
        <member name="T:Accord.DeviceBusyException">
            <summary>
            Device busy exception.
            </summary>
            
            <remarks><para>The exception is thrown in the case if access to certain device
            is not available due to the fact that it is currently busy handling other request/connection.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.DeviceBusyException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DeviceBusyException"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.DeviceBusyException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DeviceBusyException"/> class.
            </summary>
            
            <param name="message">Exception's message.</param>
            
        </member>
        <member name="M:Accord.DeviceBusyException.#ctor(System.String,System.Exception)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.DeviceBusyException"/> class.
            </summary>
            
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
            
        </member>
        <member name="M:Accord.DeviceBusyException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.DeviceBusyException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
            
        </member>
        <member name="T:Accord.DeviceErrorException">
             <summary>
             Device error exception.
             </summary>
             
             <remarks><para>The exception is thrown in the case if some error happens with a device, which
             may need to be reported to user.</para></remarks>
            
        </member>
        <member name="M:Accord.DeviceErrorException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DeviceErrorException"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.DeviceErrorException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DeviceErrorException"/> class.
            </summary>
            
            <param name="message">Exception's message.</param>
            
        </member>
        <member name="M:Accord.DeviceErrorException.#ctor(System.String,System.Exception)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.DeviceErrorException"/> class.
            </summary>
            
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
            
        </member>
        <member name="M:Accord.DeviceErrorException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.DeviceErrorException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
            
        </member>
        <member name="T:Accord.IntPoint">
            <summary>
            Structure for representing a pair of coordinates of integer type.
            </summary>
            
            <remarks><para>The structure is used to store a pair of integer coordinates.</para>
            
            <para>Sample usage:</para>
            <code>
            // assigning coordinates in the constructor
            IntPoint p1 = new IntPoint( 10, 20 );
            // creating a point and assigning coordinates later
            IntPoint p2;
            p2.X = 30;
            p2.Y = 40;
            // calculating distance between two points
            float distance = p1.DistanceTo( p2 );
            </code>
            </remarks>
            
        </member>
        <member name="F:Accord.IntPoint.X">
            <summary> 
            X coordinate.
            </summary> 
            
        </member>
        <member name="F:Accord.IntPoint.Y">
            <summary> 
            Y coordinate.
            </summary> 
            
        </member>
        <member name="M:Accord.IntPoint.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.IntPoint"/> structure.
            </summary>
            
            <param name="x">X axis coordinate.</param>
            <param name="y">Y axis coordinate.</param>
            
        </member>
        <member name="M:Accord.IntPoint.DistanceTo(Accord.IntPoint)">
            <summary>
            Calculate Euclidean distance between two points.
            </summary>
            
            <param name="anotherPoint">Point to calculate distance to.</param>
            
            <returns>Returns Euclidean distance between this point and
            <paramref name="anotherPoint"/> points.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.SquaredDistanceTo(Accord.Point)">
            <summary>
            Calculate squared Euclidean distance between two points.
            </summary>
            
            <param name="anotherPoint">Point to calculate distance to.</param>
            
            <returns>Returns squared Euclidean distance between this point and
            <paramref name="anotherPoint"/> points.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Addition(Accord.IntPoint,Accord.IntPoint)">
            <summary>
            Addition operator - adds values of two points.
            </summary>
            
            <param name="point1">First point for addition.</param>
            <param name="point2">Second point for addition.</param>
            
            <returns>Returns new point which coordinates equal to sum of corresponding
            coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.Add(Accord.IntPoint,Accord.IntPoint)">
            <summary>
            Addition operator - adds values of two points.
            </summary>
            
            <param name="point1">First point for addition.</param>
            <param name="point2">Second point for addition.</param>
            
            <returns>Returns new point which coordinates equal to sum of corresponding
            coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Subtraction(Accord.IntPoint,Accord.IntPoint)">
             <summary>
             Subtraction operator - subtracts values of two points.
             </summary>
             
             <param name="point1">Point to subtract from.</param>
             <param name="point2">Point to subtract.</param>
             
             <returns>Returns new point which coordinates equal to difference of corresponding
             coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.Subtract(Accord.IntPoint,Accord.IntPoint)">
             <summary>
             Subtraction operator - subtracts values of two points.
             </summary>
             
             <param name="point1">Point to subtract from.</param>
             <param name="point2">Point to subtract.</param>
             
             <returns>Returns new point which coordinates equal to difference of corresponding
             coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Addition(Accord.IntPoint,System.Int32)">
            <summary>
            Addition operator - adds scalar to the specified point.
            </summary>
            
            <param name="point">Point to increase coordinates of.</param>
            <param name="valueToAdd">Value to add to coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point increased by specified value.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.Add(Accord.IntPoint,System.Int32)">
            <summary>
            Addition operator - adds scalar to the specified point.
            </summary>
            
            <param name="point">Point to increase coordinates of.</param>
            <param name="valueToAdd">Value to add to coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point increased by specified value.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Subtraction(Accord.IntPoint,System.Int32)">
            <summary>
            Subtraction operator - subtracts scalar from the specified point.
            </summary>
            
            <param name="point">Point to decrease coordinates of.</param>
            <param name="valueToSubtract">Value to subtract from coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point decreased by specified value.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.Subtract(Accord.IntPoint,System.Int32)">
            <summary>
            Subtraction operator - subtracts scalar from the specified point.
            </summary>
            
            <param name="point">Point to decrease coordinates of.</param>
            <param name="valueToSubtract">Value to subtract from coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point decreased by specified value.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Multiply(Accord.IntPoint,System.Int32)">
             <summary>
             Multiplication operator - multiplies coordinates of the specified point by scalar value.
             </summary>
             
             <param name="point">Point to multiply coordinates of.</param>
             <param name="factor">Multiplication factor.</param>
             
             <returns>Returns new point which coordinates equal to coordinates of
             the specified point multiplied by specified value.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.Multiply(Accord.IntPoint,System.Int32)">
             <summary>
             Multiplication operator - multiplies coordinates of the specified point by scalar value.
             </summary>
             
             <param name="point">Point to multiply coordinates of.</param>
             <param name="factor">Multiplication factor.</param>
             
             <returns>Returns new point which coordinates equal to coordinates of
             the specified point multiplied by specified value.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Division(Accord.IntPoint,System.Int32)">
            <summary>
            Division operator - divides coordinates of the specified point by scalar value.
            </summary>
            
            <param name="point">Point to divide coordinates of.</param>
            <param name="factor">Division factor.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point divided by specified value.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.Divide(Accord.IntPoint,System.Int32)">
            <summary>
            Division operator - divides coordinates of the specified point by scalar value.
            </summary>
            
            <param name="point">Point to divide coordinates of.</param>
            <param name="factor">Division factor.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point divided by specified value.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Equality(Accord.IntPoint,Accord.IntPoint)">
             <summary>
             Equality operator - checks if two points have equal coordinates.
             </summary>
             
             <param name="point1">First point to check.</param>
             <param name="point2">Second point to check.</param>
             
             <returns>Returns <see langword="true"/> if coordinates of specified
             points are equal.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Inequality(Accord.IntPoint,Accord.IntPoint)">
             <summary>
             Inequality operator - checks if two points have different coordinates.
             </summary>
             
             <param name="point1">First point to check.</param>
             <param name="point2">Second point to check.</param>
             
             <returns>Returns <see langword="true"/> if coordinates of specified
             points are not equal.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.Equals(System.Object)">
            <summary>
            Check if this instance of <see cref="T:Accord.IntPoint"/> equal to the specified one.
            </summary>
            
            <param name="obj">Another point to check equalty to.</param>
            
            <returns>Return <see langword="true"/> if objects are equal.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.GetHashCode">
            <summary>
            Get hash code for this instance.
            </summary>
            
            <returns>Returns the hash code for this instance.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Implicit(Accord.IntPoint)~Accord.Point">
            <summary>
            Implicit conversion to <see cref="T:Accord.Point"/>.
            </summary>
            
            <param name="point">Integer point to convert to single precision point.</param>
            
            <returns>Returns new single precision point which coordinates are implicitly converted
            to floats from coordinates of the specified integer point.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.op_Implicit(Accord.IntPoint)~Accord.DoublePoint">
            <summary>
            Implicit conversion to <see cref="T:Accord.DoublePoint"/>.
            </summary>
            
            <param name="point">Integer point to convert to double precision point.</param>
            
            <returns>Returns new double precision point which coordinates are implicitly converted
            to doubles from coordinates of the specified integer point.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.ToString">
             <summary>
             Get string representation of the class.
             </summary>
             
             <returns>Returns string, which contains values of the point in readable form.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.EuclideanNorm">
            <summary>
            Calculate Euclidean norm of the vector comprised of the point's 
            coordinates - distance from (0, 0) in other words.
            </summary>
            
            <returns>Returns point's distance from (0, 0) point.</returns>
            
        </member>
        <member name="M:Accord.IntPoint.CompareTo(Accord.IntPoint)">
            <summary>
            Compares the current instance with another object of the same type and returns an integer that indicates whether the current instance precedes, follows, or occurs in the same position in the sort order as the other object.
            </summary>
            <param name="other">An object to compare with this instance.</param>
            <returns>A value that indicates the relative order of the objects being compared. The return value has these meanings: Value Meaning Less than zero This instance precedes <paramref name="other" /> in the sort order.  Zero This instance occurs in the same position in the sort order as <paramref name="other" />. Greater than zero This instance follows <paramref name="other" /> in the sort order.</returns>
        </member>
        <member name="T:Accord.IntRange">
             <summary>
               Represents an integer range with minimum and maximum values.
             </summary>
             
             <remarks>
               The class represents an integer range with inclusive limits, where
               both minimum and maximum values of the range are included into it.
               Mathematical notation of such range is <b>[min, max]</b>.
             </remarks>
             
             <example>
             <code>
             // create [1, 10] range
             var range1 = new IntRange(1, 10);
             
             // create [5, 15] range
             var range2 = new IntRange(5, 15);
             
             check if values is inside of the first range
             if (range1.IsInside(7))
             {
                 // ...
             }
             
             // check if the second range is inside of the first range
             if (range1.IsInside(range2))
             {
                 // ...
             }
             
             // check if two ranges overlap
             if (range1.IsOverlapping(range2))
             {
                 // ...
             }
             </code>
             </example>
             
             <seealso cref="T:Accord.DoubleRange"/>
             <seealso cref="T:Accord.Range"/>
             <seealso cref="T:Accord.IntRange"/>
            
        </member>
        <member name="P:Accord.IntRange.Min">
            <summary>
              Minimum value of the range.
            </summary>
            
            <remarks>
              Represents minimum value (left side limit) of the range [<b>min</b>, max].
            </remarks>
            
        </member>
        <member name="P:Accord.IntRange.Max">
            <summary>
              Maximum value of the range.
            </summary>
            
            <remarks>
              Represents maximum value (right side limit) of the range [min, <b>max</b>].
            </remarks>
            
        </member>
        <member name="P:Accord.IntRange.Length">
            <summary>
              Gets the length of the range, defined as (max - min).
            </summary>
            
        </member>
        <member name="M:Accord.IntRange.#ctor(System.Int32,System.Int32)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.IntRange"/> class.
            </summary>
            
            <param name="min">Minimum value of the range.</param>
            <param name="max">Maximum value of the range.</param>
            
        </member>
        <member name="M:Accord.IntRange.IsInside(System.Int32)">
            <summary>
              Check if the specified value is inside of the range.
            </summary>
            
            <param name="x">Value to check.</param>
            
            <returns>
              <b>True</b> if the specified value is inside of the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.Intersection(Accord.IntRange)">
            <summary>
              Computes the intersection between two ranges.
            </summary>
            
            <param name="range">The second range for which the intersection should be calculated.</param>
            
            <returns>An new <see cref="T:Accord.IntRange"/> structure containing the intersection
            between this range and the <paramref name="range"/> given as argument.</returns>
            
        </member>
        <member name="M:Accord.IntRange.IsInside(Accord.IntRange)">
            <summary>
              Check if the specified range is inside of the range.
            </summary>
            
            <param name="range">Range to check.</param>
            
            <returns>
              <b>True</b> if the specified range is inside of the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.IsOverlapping(Accord.IntRange)">
            <summary>
              Check if the specified range overlaps with the range.
            </summary>
            
            <param name="range">Range to check for overlapping.</param>
            
            <returns>
              <b>True</b> if the specified range overlaps with the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.op_Equality(Accord.IntRange,Accord.IntRange)">
            <summary>
              Determines whether two instances are equal.
            </summary>
            
        </member>
        <member name="M:Accord.IntRange.op_Inequality(Accord.IntRange,Accord.IntRange)">
            <summary>
              Determines whether two instances are not equal.
            </summary>
            
        </member>
        <member name="M:Accord.IntRange.Equals(Accord.IntRange)">
            <summary>
              Indicates whether the current object is equal to another object of the same type.
            </summary>
            
            <param name="other">An object to compare with this object.</param>
            
            <returns>
              true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.Equals(System.Object)">
            <summary>
              Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
            
            <param name="obj">The <see cref="T:System.Object" /> to compare with this instance.</param>
            
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.GetHashCode">
            <summary>
              Returns a hash code for this instance.
            </summary>
            
            <returns>
              A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.ToString">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.ToString(System.String,System.IFormatProvider)">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.op_Implicit(Accord.IntRange)~Accord.DoubleRange">
            <summary>
              Performs an implicit conversion from <see cref="T:Accord.IntRange"/> to <see cref="T:Accord.DoubleRange"/>.
            </summary>
            
            <param name="range">The range.</param>
            
            <returns>
              The result of the conversion.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.op_Implicit(Accord.IntRange)~Accord.Range">
            <summary>
              Performs an implicit conversion from <see cref="T:Accord.IntRange"/> to <see cref="T:Accord.Range"/>.
            </summary>
            
            <param name="range">The range.</param>
            
            <returns>
              The result of the conversion.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.GetEnumerator">
            <summary>
              Returns an enumerator that iterates through a collection.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="M:Accord.IntRange.System#Collections#IEnumerable#GetEnumerator">
            <summary>
              Returns an enumerator that iterates through a collection.
            </summary>
            
            <returns>
              An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
            
        </member>
        <member name="T:Accord.Point">
            <summary>
            Structure for representing a pair of coordinates of float type.
            </summary>
            
            <remarks><para>The structure is used to store a pair of floating point
            coordinates with single precision.</para>
            
            <para>Sample usage:</para>
            <code>
            // assigning coordinates in the constructor
            Point p1 = new Point( 10, 20 );
            // creating a point and assigning coordinates later
            Point p2;
            p2.X = 30;
            p2.Y = 40;
            // calculating distance between two points
            float distance = p1.DistanceTo( p2 );
            </code>
            </remarks>
            
        </member>
        <member name="F:Accord.Point.X">
            <summary> 
            X coordinate.
            </summary> 
            
        </member>
        <member name="F:Accord.Point.Y">
            <summary> 
            Y coordinate.
            </summary> 
            
        </member>
        <member name="M:Accord.Point.#ctor(System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.Point"/> structure.
            </summary>
            
            <param name="x">X axis coordinate.</param>
            <param name="y">Y axis coordinate.</param>
            
        </member>
        <member name="M:Accord.Point.DistanceTo(Accord.Point)">
            <summary>
            Calculate Euclidean distance between two points.
            </summary>
            
            <param name="anotherPoint">Point to calculate distance to.</param>
            
            <returns>Returns Euclidean distance between this point and
            <paramref name="anotherPoint"/> points.</returns>
            
        </member>
        <member name="M:Accord.Point.SquaredDistanceTo(Accord.Point)">
            <summary>
            Calculate squared Euclidean distance between two points.
            </summary>
            
            <param name="anotherPoint">Point to calculate distance to.</param>
            
            <returns>Returns squared Euclidean distance between this point and
            <paramref name="anotherPoint"/> points.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Addition(Accord.Point,Accord.Point)">
            <summary>
            Addition operator - adds values of two points.
            </summary>
            
            <param name="point1">First point for addition.</param>
            <param name="point2">Second point for addition.</param>
            
            <returns>Returns new point which coordinates equal to sum of corresponding
            coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.Point.Add(Accord.Point,Accord.Point)">
            <summary>
            Addition operator - adds values of two points.
            </summary>
            
            <param name="point1">First point for addition.</param>
            <param name="point2">Second point for addition.</param>
            
            <returns>Returns new point which coordinates equal to sum of corresponding
            coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Subtraction(Accord.Point,Accord.Point)">
             <summary>
             Subtraction operator - subtracts values of two points.
             </summary>
             
             <param name="point1">Point to subtract from.</param>
             <param name="point2">Point to subtract.</param>
             
             <returns>Returns new point which coordinates equal to difference of corresponding
             coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.Point.Subtract(Accord.Point,Accord.Point)">
             <summary>
             Subtraction operator - subtracts values of two points.
             </summary>
             
             <param name="point1">Point to subtract from.</param>
             <param name="point2">Point to subtract.</param>
             
             <returns>Returns new point which coordinates equal to difference of corresponding
             coordinates of specified points.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Addition(Accord.Point,System.Single)">
            <summary>
            Addition operator - adds scalar to the specified point.
            </summary>
            
            <param name="point">Point to increase coordinates of.</param>
            <param name="valueToAdd">Value to add to coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point increased by specified value.</returns>
            
        </member>
        <member name="M:Accord.Point.Add(Accord.Point,System.Single)">
            <summary>
            Addition operator - adds scalar to the specified point.
            </summary>
            
            <param name="point">Point to increase coordinates of.</param>
            <param name="valueToAdd">Value to add to coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point increased by specified value.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Subtraction(Accord.Point,System.Single)">
            <summary>
            Subtraction operator - subtracts scalar from the specified point.
            </summary>
            
            <param name="point">Point to decrease coordinates of.</param>
            <param name="valueToSubtract">Value to subtract from coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point decreased by specified value.</returns>
            
        </member>
        <member name="M:Accord.Point.Subtract(Accord.Point,System.Single)">
            <summary>
            Subtraction operator - subtracts scalar from the specified point.
            </summary>
            
            <param name="point">Point to decrease coordinates of.</param>
            <param name="valueToSubtract">Value to subtract from coordinates of the specified point.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point decreased by specified value.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Multiply(Accord.Point,System.Single)">
             <summary>
             Multiplication operator - multiplies coordinates of the specified point by scalar value.
             </summary>
             
             <param name="point">Point to multiply coordinates of.</param>
             <param name="factor">Multiplication factor.</param>
             
             <returns>Returns new point which coordinates equal to coordinates of
             the specified point multiplied by specified value.</returns>
            
        </member>
        <member name="M:Accord.Point.Multiply(Accord.Point,System.Single)">
             <summary>
             Multiplication operator - multiplies coordinates of the specified point by scalar value.
             </summary>
             
             <param name="point">Point to multiply coordinates of.</param>
             <param name="factor">Multiplication factor.</param>
             
             <returns>Returns new point which coordinates equal to coordinates of
             the specified point multiplied by specified value.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Division(Accord.Point,System.Single)">
            <summary>
            Division operator - divides coordinates of the specified point by scalar value.
            </summary>
            
            <param name="point">Point to divide coordinates of.</param>
            <param name="factor">Division factor.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point divided by specified value.</returns>
            
        </member>
        <member name="M:Accord.Point.Divide(Accord.Point,System.Single)">
            <summary>
            Division operator - divides coordinates of the specified point by scalar value.
            </summary>
            
            <param name="point">Point to divide coordinates of.</param>
            <param name="factor">Division factor.</param>
            
            <returns>Returns new point which coordinates equal to coordinates of
            the specified point divided by specified value.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Equality(Accord.Point,Accord.Point)">
             <summary>
             Equality operator - checks if two points have equal coordinates.
             </summary>
             
             <param name="point1">First point to check.</param>
             <param name="point2">Second point to check.</param>
             
             <returns>Returns <see langword="true"/> if coordinates of specified
             points are equal.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Inequality(Accord.Point,Accord.Point)">
             <summary>
             Inequality operator - checks if two points have different coordinates.
             </summary>
             
             <param name="point1">First point to check.</param>
             <param name="point2">Second point to check.</param>
             
             <returns>Returns <see langword="true"/> if coordinates of specified
             points are not equal.</returns>
            
        </member>
        <member name="M:Accord.Point.Equals(System.Object)">
            <summary>
            Check if this instance of <see cref="T:Accord.Point"/> equal to the specified one.
            </summary>
            
            <param name="obj">Another point to check equalty to.</param>
            
            <returns>Return <see langword="true"/> if objects are equal.</returns>
            
        </member>
        <member name="M:Accord.Point.GetHashCode">
            <summary>
            Get hash code for this instance.
            </summary>
            
            <returns>Returns the hash code for this instance.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Explicit(Accord.Point)~Accord.IntPoint">
            <summary>
            Explicit conversion to <see cref="T:Accord.IntPoint"/>.
            </summary>
            
            <param name="point">Single precision point to convert to integer point.</param>
            
            <returns>Returns new integer point which coordinates are explicitly converted
            to integers from coordinates of the specified single precision point by
            casting float values to integers value.</returns>
            
        </member>
        <member name="M:Accord.Point.op_Implicit(Accord.Point)~Accord.DoublePoint">
            <summary>
            Implicit conversion to <see cref="T:Accord.DoublePoint"/>.
            </summary>
            
            <param name="point">Single precision point to convert to double precision point.</param>
            
            <returns>Returns new double precision point which coordinates are implicitly converted
            to doubles from coordinates of the specified single precision point.</returns>
            
        </member>
        <member name="M:Accord.Point.Round">
            <summary>
            Rounds the single precision point.
            </summary>
            
            <returns>Returns new integer point, which coordinates equal to whole numbers
            nearest to the corresponding coordinates of the single precision point.</returns>
            
        </member>
        <member name="M:Accord.Point.ToString">
             <summary>
             Get string representation of the class.
             </summary>
             
             <returns>Returns string, which contains values of the point in readable form.</returns>
            
        </member>
        <member name="M:Accord.Point.EuclideanNorm">
            <summary>
            Calculate Euclidean norm of the vector comprised of the point's 
            coordinates - distance from (0, 0) in other words.
            </summary>
            
            <returns>Returns point's distance from (0, 0) point.</returns>
            
        </member>
        <member name="T:Accord.PolishExpression">
            <summary>
            Evaluator of expressions written in reverse polish notation.
            </summary>
            
            <remarks><para>The class evaluates expressions writen in reverse postfix polish notation.</para>
            
            <para>The list of supported functuins is:</para>
            <list type="bullet">
            <item><b>Arithmetic functions</b>: +, -, *, /;</item>
            <item><b>sin</b> - sine;</item>
            <item><b>cos</b> - cosine;</item>
            <item><b>ln</b> - natural logarithm;</item>
            <item><b>exp</b> - exponent;</item>
            <item><b>sqrt</b> - square root.</item>
            </list>
            
            <para>Arguments for these functions could be as usual constants, written as numbers, as variables,
            writen as $&lt;var_number&gt; (<b>$2</b>, for example). The variable number is zero based index
            of variables array.</para>
            
            <para>Sample usage:</para>
            <code>
            // expression written in polish notation
            string expression = "2 $0 / 3 $1 * +";
            // variables for the expression
            double[] vars = new double[] { 3, 4 };
            // expression evaluation
            double result = PolishExpression.Evaluate( expression, vars );
            </code>
            </remarks>
            
        </member>
        <member name="M:Accord.PolishExpression.Evaluate(System.String,System.Double[])">
             <summary>
             Evaluates specified expression.
             </summary>
            
             <param name="expression">Expression written in postfix polish notation.</param>
             <param name="variables">Variables for the expression.</param>
             
             <returns>Evaluated value of the expression.</returns>
             
             <exception cref="T:System.ArgumentException">Unsupported function is used in the expression.</exception>
             <exception cref="T:System.ArgumentException">Incorrect postfix polish expression.</exception>
            
        </member>
        <member name="T:Accord.Range">
            <summary>
              Represents a double range with minimum and maximum values,
              where values are single-precision floating-point (float) numbers.
            </summary>
            
            <remarks>
              This class represents a double range with inclusive limits, where
              both minimum and maximum values of the range are included into it.
              Mathematical notation of such range is <b>[min, max]</b>.
            </remarks>
            
            <example>
            <code>
            // create [0.25, 1.5] range
            var range1 = new SingleRange(0.25f, 1.5f);
            
            // create [1.00, 2.25] range
            var range2 = new SingleRange(1.00f, 2.25f);
            
            // check if values is inside of the first range
            if (range1.IsInside(0.75f))
            {
                // ...
            }
            
            // check if the second range is inside of the first range
            if (range1.IsInside(range2))
            {
                // ...
            }
            
            // check if two ranges overlap
            if (range1.IsOverlapping(range2))
            {
                // ...
            }
            </code>
            </example>
            
            <seealso cref="T:Accord.IntRange"/>
            <seealso cref="T:Accord.DoubleRange"/>
            <seealso cref="T:Accord.ByteRange"/>
            
        </member>
        <member name="P:Accord.Range.Min">
            <summary>
              Minimum value of the range.
            </summary>
            
            <remarks>
              Represents minimum value (left side limit) of the range [<b>min</b>, max].
            </remarks>
            
        </member>
        <member name="P:Accord.Range.Max">
            <summary>
              Maximum value of the range.
            </summary>
            
            <remarks>
              Represents maximum value (right side limit) of the range [min, <b>max</b>].
            </remarks>
            
        </member>
        <member name="P:Accord.Range.Length">
            <summary>
              Gets the length of the range, defined as (max - min).
            </summary>
            
        </member>
        <member name="M:Accord.Range.#ctor(System.Single,System.Single)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.Range"/> class.
            </summary>
            
            <param name="min">Minimum value of the range.</param>
            <param name="max">Maximum value of the range.</param>
            
        </member>
        <member name="M:Accord.Range.IsInside(System.Single)">
            <summary>
              Check if the specified value is inside of the range.
            </summary>
            
            <param name="x">Value to check.</param>
            
            <returns>
              <b>True</b> if the specified value is inside of the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.Range.IsInside(Accord.Range)">
            <summary>
              Check if the specified range is inside of the range.
            </summary>
            
            <param name="range">Range to check.</param>
            
            <returns>
              <b>True</b> if the specified range is inside of the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.Range.IsOverlapping(Accord.Range)">
            <summary>
              Check if the specified range overlaps with the range.
            </summary>
            
            <param name="range">Range to check for overlapping.</param>
            
            <returns>
              <b>True</b> if the specified range overlaps with the range or <b>false</b> otherwise.
            </returns>
            
        </member>
        <member name="M:Accord.Range.Intersection(Accord.Range)">
            <summary>
              Computes the intersection between two ranges.
            </summary>
            
            <param name="range">The second range for which the intersection should be calculated.</param>
            
            <returns>An new <see cref="T:Accord.Range"/> structure containing the intersection
            between this range and the <paramref name="range"/> given as argument.</returns>
            
        </member>
        <member name="M:Accord.Range.op_Equality(Accord.Range,Accord.Range)">
            <summary>
              Determines whether two instances are equal.
            </summary>
            
        </member>
        <member name="M:Accord.Range.op_Inequality(Accord.Range,Accord.Range)">
            <summary>
              Determines whether two instances are not equal.
            </summary>
            
        </member>
        <member name="M:Accord.Range.Equals(Accord.Range)">
            <summary>
              Indicates whether the current object is equal to another object of the same type.
            </summary>
            
            <param name="other">An object to compare with this object.</param>
            
            <returns>
              true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
            
        </member>
        <member name="M:Accord.Range.Equals(System.Object)">
            <summary>
              Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
            
            <param name="obj">The <see cref="T:System.Object" /> to compare with this instance.</param>
            
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            
        </member>
        <member name="M:Accord.Range.GetHashCode">
            <summary>
              Returns a hash code for this instance.
            </summary>
            
            <returns>
              A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
            
        </member>
        <member name="M:Accord.Range.ToString">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="M:Accord.Range.ToString(System.String,System.IFormatProvider)">
            <summary>
              Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            
            <returns>
              A <see cref="T:System.String" /> that represents this instance.
            </returns>
            
        </member>
        <member name="M:Accord.Range.op_Implicit(Accord.Range)~Accord.DoubleRange">
            <summary>
              Performs an implicit conversion from <see cref="T:Accord.IntRange"/> to <see cref="T:Accord.DoubleRange"/>.
            </summary>
            
            <param name="range">The range.</param>
            
            <returns>
              The result of the conversion.
            </returns>
            
        </member>
        <member name="M:Accord.Range.ToIntRange(System.Boolean)">
             <summary>
               Converts this single-precision range into an <see cref="T:Accord.IntRange"/>.
             </summary>
             
             <param name="provideInnerRange">
               Specifies if inner integer range must be returned or outer range.</param>
             
             <returns>Returns integer version of the range.</returns>
             
             <remarks>
               If <paramref name="provideInnerRange"/> is set to <see langword="true"/>, then the
               returned integer range will always fit inside of the current single precision range.
               If it is set to <see langword="false"/>, then current single precision range will always
               fit into the returned integer range.
             </remarks>
            
        </member>
        <member name="T:Accord.SystemTools">
            <summary>
            Set of systems tools.
            </summary>
            
            <remarks><para>The class is a container of different system tools, which are used
            across the framework. Some of these tools are platform specific, so their
            implementation is different on different platform, like .NET and Mono.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.SystemTools.IsRunningOnMono">
            <summary>
            Determines whether the code is running under Mono. 
            </summary>
            
        </member>
        <member name="M:Accord.SystemTools.CopyUnmanagedMemory(System.IntPtr,System.IntPtr,System.Int32)">
             <summary>
             Copy block of unmanaged memory.
             </summary>
             
             <param name="dst">Destination pointer.</param>
             <param name="src">Source pointer.</param>
             <param name="count">Memory block's length to copy.</param>
             
             <returns>Return's value of <paramref name="dst"/> - pointer to destination.</returns>
             
             <remarks><para>This function is required because of the fact that .NET does
             not provide any way to copy unmanaged blocks, but provides only methods to
             copy from unmanaged memory to managed memory and vise versa.</para></remarks>
            
        </member>
        <member name="M:Accord.SystemTools.CopyUnmanagedMemory(System.Byte*,System.Byte*,System.Int32)">
            <summary>
            Copy block of unmanaged memory.
            </summary>
            
            <param name="dst">Destination pointer.</param>
            <param name="src">Source pointer.</param>
            <param name="count">Memory block's length to copy.</param>
            
            <returns>Return's value of <paramref name="dst"/> - pointer to destination.</returns>
            
            <remarks><para>This function is required because of the fact that .NET does
            not provide any way to copy unmanaged blocks, but provides only methods to
            copy from unmanaged memory to managed memory and vise versa.</para></remarks>
            
        </member>
        <member name="M:Accord.SystemTools.SetUnmanagedMemory(System.IntPtr,System.Int32,System.Int32)">
            <summary>
            Fill memory region with specified value.
            </summary>
            
            <param name="dst">Destination pointer.</param>
            <param name="filler">Filler byte's value.</param>
            <param name="count">Memory block's length to fill.</param>
            
            <returns>Return's value of <paramref name="dst"/> - pointer to destination.</returns>
            
        </member>
        <member name="M:Accord.SystemTools.SetUnmanagedMemory(System.Byte*,System.Int32,System.Int32)">
            <summary>
            Fill memory region with specified value.
            </summary>
            
            <param name="dst">Destination pointer.</param>
            <param name="filler">Filler byte's value.</param>
            <param name="count">Memory block's length to fill.</param>
            
            <returns>Return's value of <paramref name="dst"/> - pointer to destination.</returns>
            
        </member>
        <member name="T:Accord.ThreadSafeRandom">
            <summary>
            Thread safe version of the <see cref="T:System.Random"/> class.
            </summary>
            
            <remarks><para>The class inherits the <see cref="T:System.Random"/> and overrides
            its random numbers generation methods providing thread safety by guarding call
            to the base class with a lock. See documentation to <see cref="T:System.Random"/> for
            additional information about the base class.</para></remarks>
            
        </member>
        <member name="M:Accord.ThreadSafeRandom.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.ThreadSafeRandom"/> class.
            </summary>
            
            <remarks>See <see cref="M:System.Random.Next"/> for more information.</remarks>
            
        </member>
        <member name="M:Accord.ThreadSafeRandom.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.ThreadSafeRandom"/> class.
            </summary>
            
            <remarks>A number used to calculate a starting value for the pseudo-random number sequence.
            If a negative number is specified, the absolute value of the number is used.</remarks>
            
            
            <remarks>See <see cref="M:System.Random.Next"/> for more information.</remarks>
            
        </member>
        <member name="M:Accord.ThreadSafeRandom.Next">
            <summary>
            Returns a nonnegative random number.
            </summary>
            
            <returns>Returns a 32-bit signed integer greater than or equal to zero and less than
            <see cref="F:System.Int32.MaxValue"/>.</returns>
            
            <remarks>See <see cref="M:System.Random.Next"/> for more information.</remarks>
            
        </member>
        <member name="M:Accord.ThreadSafeRandom.Next(System.Int32)">
            <summary>
            Returns a nonnegative random number less than the specified maximum.
            </summary>
            
            <param name="maxValue">The exclusive upper bound of the random number to be generated.
            <paramref name="maxValue"/> must be greater than or equal to zero.</param>
            
            <returns>Returns a 32-bit signed integer greater than or equal to zero, and less than <paramref name="maxValue"/>;
            that is, the range of return values ordinarily includes zero but not <paramref name="maxValue"/>.</returns>
            
            <remarks>See <see cref="M:System.Random.Next(System.Int32)"/> for more information.</remarks>
            
        </member>
        <member name="M:Accord.ThreadSafeRandom.Next(System.Int32,System.Int32)">
             <summary>
             Returns a random number within a specified range.
             </summary>
             
             <param name="minValue">The inclusive lower bound of the random number returned.</param>
             <param name="maxValue">The exclusive upper bound of the random number returned.
             <paramref name="maxValue"/> must be greater than or equal to <paramref name="minValue"/>.</param>
             
             <returns>Returns a 32-bit signed integer greater than or equal to <paramref name="minValue"/> and less
             than <paramref name="maxValue"/>; that is, the range of return values includes
             <paramref name="minValue"/> but not <paramref name="maxValue"/>.</returns>
             
             <remarks>See <see cref="M:System.Random.Next(System.Int32,System.Int32)"/> for more information.</remarks>
            
        </member>
        <member name="M:Accord.ThreadSafeRandom.NextBytes(System.Byte[])">
             <summary>
             Fills the elements of a specified array of bytes with random numbers.
             </summary>
             
             <param name="buffer">An array of bytes to contain random numbers.</param>
             
             <remarks>See <see cref="M:System.Random.NextBytes(System.Byte[])"/> for more information.</remarks>
            
        </member>
        <member name="M:Accord.ThreadSafeRandom.NextDouble">
             <summary>
             Returns a random number between 0.0 and 1.0.
             </summary>
             
             <returns>Returns a double-precision floating point number greater than or equal to 0.0, and less than 1.0.</returns>
             
             <remarks>See <see cref="M:System.Random.NextDouble"/> for more information.</remarks>
            
        </member>
        <member name="T:Accord.PositiveAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be greater than zero.
            </summary>
            
        </member>
        <member name="M:Accord.PositiveAttribute.#ctor(System.Double,System.Double)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.PositiveAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.NegativeAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be lesser than zero.
            </summary>
            
        </member>
        <member name="M:Accord.NegativeAttribute.#ctor(System.Double,System.Double)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.NegativeAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.NonpositiveAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be lesser than or equal to zero.
            </summary>
            
        </member>
        <member name="M:Accord.NonpositiveAttribute.#ctor(System.Double,System.Double)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.NonpositiveAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.NonnegativeAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be greater than or equal to zero.
            </summary>
            
        </member>
        <member name="M:Accord.NonnegativeAttribute.#ctor(System.Double,System.Double)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.NonnegativeAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.RealAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be real (double).
            </summary>
            
        </member>
        <member name="M:Accord.RealAttribute.#ctor(System.Double,System.Double)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.RealAttribute"/> class.
            </summary>
            
        </member>
        <member name="P:Accord.RealAttribute.Minimum">
            <summary>
              Gets the minimum allowed field value.
            </summary>
            
        </member>
        <member name="P:Accord.RealAttribute.Maximum">
            <summary>
              Gets the maximum allowed field value.
            </summary>
            
        </member>
        <member name="T:Accord.UnitAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be real between 0 and 1.
            </summary>
            
        </member>
        <member name="M:Accord.UnitAttribute.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.UnitAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.PositiveIntegerAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be an integer bigger than zero.
            </summary>
            
        </member>
        <member name="M:Accord.PositiveIntegerAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.PositiveIntegerAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.NegativeIntegerAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be an integer less than zero.
            </summary>
            
        </member>
        <member name="M:Accord.NegativeIntegerAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.NegativeIntegerAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.NonpositiveIntegerAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be an integer smaller than or equal to zero.
            </summary>
            
        </member>
        <member name="M:Accord.NonpositiveIntegerAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.NonpositiveIntegerAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.NonnegativeIntegerAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be an integer bigger than or equal to zero.
            </summary>
            
        </member>
        <member name="M:Accord.NonnegativeIntegerAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.NonnegativeIntegerAttribute"/> class.
            </summary>
            
        </member>
        <member name="T:Accord.IntegerAttribute">
            <summary>
              Specifies that an argument, in a method or function,
              must be an integer.
            </summary>
            
        </member>
        <member name="M:Accord.IntegerAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.IntegerAttribute"/> class.
            </summary>
            
        </member>
        <member name="P:Accord.IntegerAttribute.Minimum">
            <summary>
              Gets the minimum allowed field value.
            </summary>
            
        </member>
        <member name="P:Accord.IntegerAttribute.Maximum">
            <summary>
              Gets the maximum allowed field value.
            </summary>
            
        </member>
        <member name="T:Accord.cast`2">
            <summary>
              Runtime cast.
            </summary>
            
            <typeparam name="T">The target type.</typeparam>
            <typeparam name="U">The source type.</typeparam>
            
        </member>
        <member name="P:Accord.cast`2.Value">
            <summary>
              Gets the value being casted.
            </summary>
            
        </member>
        <member name="M:Accord.cast`2.#ctor(`1)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.cast`2"/> struct.
            </summary>
            
        </member>
        <member name="M:Accord.cast`2.op_Implicit(`1)~Accord.cast{`0,`1}">
            <summary>
            Performs an implicit conversion from <typeparamref name="U"/> to <see cref="T:Accord.cast`2"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`2.op_Implicit(Accord.cast{`0,`1})~`0">
            <summary>
            Performs an implicit conversion from <see cref="T:Accord.cast`2"/> to <typeparamref name="T"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="T:Accord.cast`1">
            <summary>
              Runtime cast.
            </summary>
            
            <typeparam name="T">The target type.</typeparam>
            
        </member>
        <member name="P:Accord.cast`1.Value">
            <summary>
              Gets the value being casted.
            </summary>
            
        </member>
        <member name="M:Accord.cast`1.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.cast`1"/> struct.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.Double)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Double"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.Single)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Single"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.Decimal)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Decimal"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.Byte)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Byte"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.SByte)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.SByte"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.Int16)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Int16"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.UInt16)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.UInt16"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.Int32)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Int32"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.UInt32)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.UInt32"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.Int64)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Int64"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(System.UInt64)~Accord.cast{`0}">
            <summary>
            Performs an implicit conversion from <see cref="T:System.UInt64"/> to <see cref="T:Accord.cast`1"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Accord.cast`1.op_Implicit(Accord.cast{`0})~`0">
            <summary>
            Performs an implicit conversion from <see cref="T:Accord.cast`1"/> to <typeparamref name="T"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="T:Accord.ExtensionMethods">
            <summary>
              Static class for utility extension methods.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.Add(System.Data.DataColumnCollection,System.String[])">
            <summary>
              Creates and adds multiple <see cref="T:System.Data.DataColumn"/>
              objects with the given names at once.
            </summary>
            
            <param name="collection">The <see cref="T:System.Data.DataColumnCollection"/>
            to add in.</param>
            <param name="columnNames">The names of the <see cref="T:System.Data.DataColumn"/> to
            be created and added.</param>
            
            <example>
              <code>
              DataTable table = new DataTable();
              
              // Add multiple columns at once:
              table.Columns.Add("columnName1", "columnName2");
              </code>
            </example>
            
        </member>
        <member name="M:Accord.ExtensionMethods.Add(System.Data.DataColumnCollection,Accord.Collections.OrderedDictionary{System.String,System.Type})">
            <summary>
              Creates and adds multiple <see cref="T:System.Data.DataColumn"/>
              objects with the given names at once.
            </summary>
            
            <param name="collection">The <see cref="T:System.Data.DataColumnCollection"/>
              to add in.</param>
            <param name="columns">The names of the <see cref="T:System.Data.DataColumn"/>s to
              be created and added, alongside with their types.</param>
            
            <example>
              <code>
              DataTable table = new DataTable();
              
              // Add multiple columns at once:
              table.Columns.Add(new OrderedDictionary&gt;String, Type&lt;()
              {
                  { "columnName1", typeof(int)    },
                  { "columnName2", typeof(double) },
              });
              </code>
            </example>
            
        </member>
        <member name="M:Accord.ExtensionMethods.GetDescription``1(``0)">
            <summary>
              Gets a the value of a <see cref="T:System.ComponentModel.DescriptionAttribute"/>
              associated with a particular enumeration value.
            </summary>
            
            <typeparam name="T">The enumeration type.</typeparam>
            <param name="source">The enumeration value.</param>
            
            <returns>The string value stored in the value's description attribute.</returns>
            
        </member>
        <member name="M:Accord.ExtensionMethods.Read``1(System.IO.BinaryReader,``0@)">
            <summary>
              Reads a <c>struct</c> from a stream.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.Write``1(System.IO.BinaryWriter,``0[])">
            <summary>
              Reads a <c>struct</c> from a stream.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.Write``1(System.IO.BinaryWriter,``0[][])">
            <summary>
              Reads a <c>struct</c> from a stream.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.Write``1(System.IO.BinaryWriter,``0[0:,0:])">
            <summary>
              Reads a <c>struct</c> from a stream.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.ReadJagged``1(System.IO.BinaryReader,System.Int32,System.Int32)">
            <summary>
              Reads a <c>struct</c> from a stream.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.ReadMatrix``1(System.IO.BinaryReader,System.Int32,System.Int32)">
            <summary>
              Reads a <c>struct</c> from a stream.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.ReadMatrix(System.IO.BinaryReader,System.Type,System.Int32[])">
            <summary>
              Reads a <c>struct</c> from a stream.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.GetPosition(System.IO.StreamReader)">
            <summary>
              Gets the underlying buffer position for a StreamReader.
            </summary>
            
            <param name="reader">A StreamReader whose position will be retrieved.</param>
            
            <returns>The current offset from the beginning of the 
              file that the StreamReader is currently located into.</returns>
            
        </member>
        <member name="M:Accord.ExtensionMethods.DeserializeAnyVersion``1(System.Runtime.Serialization.Formatters.Binary.BinaryFormatter,System.IO.Stream)">
            <summary>
              Deserializes the specified stream into an object graph, but locates
              types by searching all loaded assemblies and ignoring their versions.
            </summary>
            
            <param name="formatter">The binary formatter.</param>
            <param name="stream">The stream from which to deserialize the object graph.</param>
            
            <returns>The top (root) of the object graph.</returns>
            
        </member>
        <member name="M:Accord.ExtensionMethods.To``1(System.Object)">
            <summary>
              Converts an object into another type, irrespective of whether
              the conversion can be done at compile time or not. This can be
              used to convert generic types to numeric types during runtime.
            </summary>
            
            <typeparam name="T">The destination type.</typeparam>
            
            <param name="value">The value to be converted.</param>
            
            <returns>The result of the conversion.</returns>
            
        </member>
        <member name="M:Accord.ExtensionMethods.To(System.Object,System.Type)">
            <summary>
              Converts an object into another type, irrespective of whether
              the conversion can be done at compile time or not. This can be
              used to convert generic types to numeric types during runtime.
            </summary>
            
            <param name="value">The value to be converted.</param>
            <param name="type">The type that the value should be converted to.</param>
            
            <returns>The result of the conversion.</returns>
            
        </member>
        <member name="M:Accord.ExtensionMethods.HasDefaultConstructor(System.Type)">
            <summary>
              Determines whether the given type has a public default (parameterless) constructor.
            </summary>
            
            <param name="t">The type to check.</param>
            
            <returns>True if the type has a public parameterless constructor; false otherwise.</returns>
            
        </member>
        <member name="M:Accord.ExtensionMethods.Format(System.String,System.Object[])">
            <summary>
              Replaces the format item in a specified string with the string
              representation of a corresponding object in a specified array.
            </summary>
            
            <param name="str">A composite format string.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
            
            <returns>
              A copy of str in which the format items have been replaced by
              the string representation of the corresponding objects in args.
            </returns>
            
        </member>
        <member name="M:Accord.ExtensionMethods.IsEqual``2(System.Collections.Generic.IDictionary{``0,``1},System.Collections.Generic.IDictionary{``0,``1})">
            <summary>
              Checks whether two dictionaries have the same contents.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.HasMethod(System.Object,System.String)">
            <summary>
              Checks whether an object implements a method with the given name.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.HasMethod``1(System.String)">
            <summary>
              Checks whether a type implements a method with the given name.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.IsGreaterThan``1(``0,System.Object)">
            <summary>
              Determines whether <c>a > b</c>.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.IsGreaterThanOrEqual``1(``0,System.Object)">
            <summary>
              Determines whether <c>a >= b</c>.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.IsLessThan``1(``0,System.Object)">
            <summary>
              Determines whether <c>a &lt; b</c>.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.IsLessThanOrEqual``1(``0,System.Object)">
            <summary>
              Determines whether <c>a &lt;= b</c>.
            </summary>
            
        </member>
        <member name="M:Accord.ExtensionMethods.GetDefaultValue(System.Type)">
            <summary>
              Gets the default value for a type. This method should serve as
              a programmatic equivalent to <c>default(T)</c>.
            </summary>
            
            <param name="type">The type whose default value should be retrieved.</param>
            
        </member>
        <member name="M:Accord.ExtensionMethods.AddressOf``1(``0)">
            <summary>
              Retrieves the memory address of a generic value type.
            </summary>
            
            <typeparam name="T">The type of the object whose address needs to be retrieved.</typeparam>
            <param name="t">The object those address needs to be retrieved.</param>
            
        </member>
        <member name="M:Accord.ExtensionMethods.AddressOfRef``1(``0@)">
            <summary>
              Retrieves the memory address of a generic reference type.
            </summary>
            
            <typeparam name="T">The type of the object whose address needs to be retrieved.</typeparam>
            <param name="t">The object those address needs to be retrieved.</param>
            
        </member>
        <member name="M:Accord.ExtensionMethods.DownloadFileWithRetry(System.Net.WebClient,System.String,System.String,System.Int32,System.Boolean)">
            <summary>
              Attempts to download a file from the web multiple times before giving up.
            </summary>
            
            <param name="client">The web client to use.</param>
            <param name="url">The URL of the file to be downloaded.</param>
            <param name="fileName">The disk location where the file should be stored.</param>
            <param name="maxAttempts">The maximum number of attempts.</param>
            <param name="overwrite">Do not overwrite <paramref name="fileName"/> if it already exists.</param>
            
        </member>
        <member name="T:Accord.ConvergenceException">
            <summary>
              Algorithm Convergence Exception.
            </summary>
            
            <remarks><para>The algorithm convergence exception is thrown in cases where a iterative
            algorithm could not converge to a finite solution.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.ConvergenceException.#ctor">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConvergenceException"/> class.
            </summary>
            
        </member>
        <member name="M:Accord.ConvergenceException.#ctor(System.String)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConvergenceException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            
        </member>
        <member name="M:Accord.ConvergenceException.#ctor(System.String,System.Exception)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConvergenceException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
            
        </member>
        <member name="M:Accord.ConvergenceException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
              Initializes a new instance of the <see cref="T:Accord.ConvergenceException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
            
        </member>
        <member name="T:Accord.DimensionMismatchException">
             <summary>
               Dimension Mismatch Exception.
             </summary>
            
             <remarks><para>The dimension mismatch exception is thrown in cases where a method expects 
             a matrix or array object having specific or compatible dimensions, such as the inner matrix
             dimensions in matrix multiplication.</para>
             </remarks>
            
        </member>
        <member name="M:Accord.DimensionMismatchException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DimensionMismatchException"/> class.
            </summary>
        </member>
        <member name="M:Accord.DimensionMismatchException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DimensionMismatchException"/> class.
            </summary>
            
            <param name="paramName">The name of the parameter that caused the current exception.</param>
            
        </member>
        <member name="M:Accord.DimensionMismatchException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DimensionMismatchException"/> class.
            </summary>
            
            <param name="paramName">The name of the parameter that caused the current exception.</param>
            <param name="message">Message providing some additional information.</param>
            
        </member>
        <member name="M:Accord.DimensionMismatchException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DimensionMismatchException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
            
        </member>
        <member name="M:Accord.DimensionMismatchException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.DimensionMismatchException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
            
        </member>
        <member name="T:Accord.NonPositiveDefiniteMatrixException">
            <summary>
              Non-Positive Definite Matrix Exception.
            </summary>
            
            <remarks><para>The non-positive definite matrix exception is thrown in cases where a method 
            expects a matrix to have only positive eigenvalues, such when dealing with covariance matrices.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.NonPositiveDefiniteMatrixException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NonPositiveDefiniteMatrixException"/> class.
            </summary>
        </member>
        <member name="M:Accord.NonPositiveDefiniteMatrixException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NonPositiveDefiniteMatrixException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            
        </member>
        <member name="M:Accord.NonPositiveDefiniteMatrixException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NonPositiveDefiniteMatrixException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
            
        </member>
        <member name="M:Accord.NonPositiveDefiniteMatrixException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NonPositiveDefiniteMatrixException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
            
        </member>
        <member name="T:Accord.NonSymmetricMatrixException">
            <summary>
              Non-Symmetric Matrix Exception.
            </summary>
            
            <remarks><para>The not symmetric matrix exception is thrown in cases where a method 
            expects a matrix to be symmetric but it is not.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.NonSymmetricMatrixException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NonSymmetricMatrixException"/> class.
            </summary>
        </member>
        <member name="M:Accord.NonSymmetricMatrixException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NonSymmetricMatrixException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            
        </member>
        <member name="M:Accord.NonSymmetricMatrixException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NonSymmetricMatrixException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
            
        </member>
        <member name="M:Accord.NonSymmetricMatrixException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.NonSymmetricMatrixException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
            
        </member>
        <member name="T:Accord.SingularMatrixException">
            <summary>
              Singular Matrix Exception.
            </summary>
            
            <remarks><para>The singular matrix exception is thrown in cases where a method which
            performs matrix inversions has encountered a non-invertible matrix during the process.</para>
            </remarks>
            
        </member>
        <member name="M:Accord.SingularMatrixException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.SingularMatrixException"/> class.
            </summary>
        </member>
        <member name="M:Accord.SingularMatrixException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.SingularMatrixException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            
        </member>
        <member name="M:Accord.SingularMatrixException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.SingularMatrixException"/> class.
            </summary>
            
            <param name="message">Message providing some additional information.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
            
        </member>
        <member name="M:Accord.SingularMatrixException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Accord.SingularMatrixException"/> class.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="info"/> parameter is null.
            </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">
            The class name is null or <see cref="P:System.Exception.HResult"/> is zero (0).
            </exception>
            
        </member>
        <member name="T:Accord.IRange`1">
            <summary>
              Common interface for Range objects, such as <see cref="T:Accord.DoubleRange"/>,
              <see cref="T:Accord.IntRange"/> or <see cref="T:Accord.Range"/>. A range represents the
              interval between two values in the form [min, max].
            </summary>
            
            <typeparam name="T">The type of the range.</typeparam>
            
            <seealso cref="T:Accord.Range"/>
            <seealso cref="T:Accord.DoubleRange"/>
            <seealso cref="T:Accord.IntRange"/>
            <seealso cref="T:Accord.ByteRange"/>
            
        </member>
        <member name="P:Accord.IRange`1.Min">
            <summary>
              Minimum value of the range.
            </summary>
            
            <remarks>
              Represents minimum value (left side limit) of the range [<b>min</b>, max].
            </remarks>
            
        </member>
        <member name="P:Accord.IRange`1.Max">
            <summary>
              Maximum value of the range.
            </summary>
            
            <remarks>
              Represents maximum value (right side limit) of the range [min, <b>max</b>].
            </remarks>
            
        </member>
        <member name="T:Accord.IO.Serializer">
            <summary>
              Model serializer. Can be used to serialize and deserialize (i.e. save and 
              load) models from the framework to and from the disk and other streams.
            </summary>
            
            <remarks>
              This class uses a binding mechanism to automatically convert files saved using 
              older versions of the framework to the new format. If a deserialization doesn't 
              work, please fill in a bug report at https://github.com/accord-net/framework/issues
            </remarks>
            
            <example>
            <para>
              The first example shows the simplest way to use the serializer to persist objects:</para>
              <code source="Unit Tests\Accord.Tests.Core\SerializerTest.cs" region="doc_simple" />
              
            <para>
              The second example shows the same, but using compression:</para>
              <code source="Unit Tests\Accord.Tests.Core\SerializerTest.cs" region="doc_compression" />
              
            <para>
              The third and last example shows a complete example on how to create, save and re-load
              a classifier from disk using serialization:</para>
            <code source="Unit Tests\Accord.Tests.MachineLearning\KNearestNeighbors\KNearestNeighborsTest.cs" region="doc_learn" />
            <code source="Unit Tests\Accord.Tests.MachineLearning\KNearestNeighbors\KNearestNeighborsTest.cs" region="doc_serialization" />
            </example>
            
        </member>
        <member name="M:Accord.IO.Serializer.Save``1(``0,System.IO.Stream,Accord.IO.SerializerCompression)">
            <summary>
              Saves an object to a stream.
            </summary>
            
            <param name="obj">The object to be serialized.</param>
            <param name="stream">The stream to which the object is to be serialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
        </member>
        <member name="M:Accord.IO.Serializer.Save``1(``0,System.Runtime.Serialization.Formatters.Binary.BinaryFormatter,System.IO.Stream,Accord.IO.SerializerCompression)">
            <summary>
              Saves an object to a stream.
            </summary>
            
            <param name="obj">The object to be serialized.</param>
            <param name="formatter">The binary formatter.</param>
            <param name="stream">The stream to which the object is to be serialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
        </member>
        <member name="M:Accord.IO.Serializer.Save``1(``0,System.String)">
            <summary>
              Saves an object to a stream.
            </summary>
            
            <param name="obj">The object to be serialized.</param>
            <param name="path">The path to the file to which the object is to be serialized.</param>
            
        </member>
        <member name="M:Accord.IO.Serializer.Save``1(``0,System.String,Accord.IO.SerializerCompression)">
            <summary>
              Saves an object to a stream.
            </summary>
            
            <param name="obj">The object to be serialized.</param>
            <param name="path">The path to the file to which the object is to be serialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
        </member>
        <member name="M:Accord.IO.Serializer.Save``1(``0,Accord.IO.SerializerCompression)">
            <summary>
              Saves an object to a stream, represented as an array of bytes.
            </summary>
            
            <param name="obj">The object to be serialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
        </member>
        <member name="M:Accord.IO.Serializer.Save``1(``0,System.Byte[]@,Accord.IO.SerializerCompression)">
            <summary>
              Saves an object to a stream.
            </summary>
            
            <param name="obj">The object to be serialized.</param>
            <param name="bytes">The sequence of bytes to which the object has been serialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.IO.Stream,Accord.IO.SerializerCompression)">
            <summary>
              Loads an object from a stream.
            </summary>
            
            <param name="stream">The stream from which the object is to be deserialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
            <returns>The deserialized machine.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.String)">
            <summary>
              Loads an object from a file.
            </summary>
            
            <param name="path">The path to the file from which the object is to be deserialized.</param>
            
            <returns>The deserialized object.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.String,Accord.IO.SerializerCompression)">
            <summary>
              Loads an object from a file.
            </summary>
            
            <param name="path">The path to the file from which the object is to be deserialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
            <returns>The deserialized object.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.Byte[],Accord.IO.SerializerCompression)">
            <summary>
              Loads an object from a stream, represented as an array of bytes.
            </summary>
            
            <param name="bytes">The byte stream containing the object to be deserialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
            <returns>The deserialized object.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.IO.Stream,``0@,Accord.IO.SerializerCompression)">
            <summary>
              Loads an object from a stream.
            </summary>
            
            <param name="stream">The stream from which the object is to be deserialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            <param name="value">The object to be read. This parameter can be used to avoid the
              need of specifying a generic argument to this function.</param>
            
            <returns>The deserialized machine.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.String,``0@)">
            <summary>
              Loads an object from a file.
            </summary>
            
            <param name="path">The path to the file from which the object is to be deserialized.</param>
            <param name="value">The object to be read. This parameter can be used to avoid the
              need of specifying a generic argument to this function.</param>
            
            <returns>The deserialized object.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.String,``0@,Accord.IO.SerializerCompression)">
            <summary>
              Loads an object from a file.
            </summary>
            
            <param name="path">The path to the file from which the object is to be deserialized.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            <param name="value">The object to be read. This parameter can be used to avoid the
              need of specifying a generic argument to this function.</param>
            
            <returns>The deserialized object.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.Byte[],``0@,Accord.IO.SerializerCompression)">
            <summary>
              Loads an object from a stream, represented as an array of bytes.
            </summary>
            
            <param name="bytes">The byte stream containing the object to be deserialized.</param>
            <param name="value">The object to be read. This parameter can be used to avoid the
              need of specifying a generic argument to this function.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
            <returns>The deserialized object.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.Load``1(System.IO.Stream,System.Runtime.Serialization.Formatters.Binary.BinaryFormatter,Accord.IO.SerializerCompression)">
            <summary>
              Loads a model from a stream.
            </summary>
            
            <typeparam name="T">The type of the model to be loaded.</typeparam>
            <param name="formatter">The binary formatter.</param>
            <param name="stream">The stream from which to deserialize the object graph.</param>
            <param name="compression">The type of compression to use. Default is None.</param>
            
            <returns>The deserialized object.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.DeepClone``1(``0)">
            <summary>
              Performs a deep copy of an object by serializing and deserializing it.
            </summary>
            
            <typeparam name="T">The type of the model to be copied.</typeparam>
            <param name="obj">The object.</param>
            
            <returns>A deep copy of the given object.</returns>
            
        </member>
        <member name="M:Accord.IO.Serializer.GetValue``1(System.Runtime.Serialization.SerializationInfo,System.String,``0@)">
            <summary>
              Retrieves a value from the SerializationInfo store.
            </summary>
            
            <typeparam name="T">The type of the value to be retrieved.</typeparam>
            <param name="info">The serialization info store containing the value.</param>
            <param name="name">The name of the value.</param>
            <param name="value">The value retrieved from the info store.</param>
            
            <returns>The value retrieved from the info store.</returns>
            
        </member>
        <member name="T:Accord.IO.SerializerCompression">
            <summary>
              Compression algorithms supported by the <see cref="T:Accord.IO.Serializer"/>.
            </summary>
            
        </member>
        <member name="F:Accord.IO.SerializerCompression.None">
            <summary>
              No serialization.
            </summary>
            
        </member>
        <member name="F:Accord.IO.SerializerCompression.GZip">
            <summary>
              Use GZip serialization.
            </summary>
            
        </member>
    </members>
</doc>
