﻿using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Media.Imaging;

namespace ChiroTrainingApp
{
    class Utilities
    {
        public static BitmapImage ConvertBitmapToBitmapImage(Bitmap bitmap)
        {
            using (MemoryStream memory = new MemoryStream())
            {
                bitmap.Save(memory, ImageFormat.Bmp);
                memory.Position = 0;
                BitmapImage bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
                bitmapImage.Freeze(); // Allows cross-thread UI updates
                return bitmapImage;
            }
        }

        public static string GetRecordingFolder()
        {
            string recordingFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "CameraRecordings");

            if (!Directory.Exists(recordingFolder))
            {
                Directory.CreateDirectory(recordingFolder);
            }

            return recordingFolder;
        }
    }
}
