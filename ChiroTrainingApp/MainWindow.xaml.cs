﻿using Accord.Video.FFMPEG;
using AForge.Video.DirectShow;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Brushes = System.Windows.Media.Brushes;

namespace ChiroTrainingApp
{        
    public partial class MainWindow : Window
    {
        private List<VideoCaptureDevice> _videoDevices = new List<VideoCaptureDevice>();
        private List<System.Windows.Controls.Image> _videoFeeds = new List<System.Windows.Controls.Image>();
        private FilterInfoCollection _videoSources;
        private Dictionary<string, bool> _isRecording = new Dictionary<string, bool>();
        private Dictionary<string, VideoFileWriter> _videoWriters = new Dictionary<string, VideoFileWriter>();
        private Dictionary<string, Button> _recordButtons = new Dictionary<string, Button>();
        private Dictionary<string, TextBlock> _recordIndicators = new Dictionary<string, TextBlock>();

        public MainWindow()
        {
            InitializeComponent();
            LoadCameraFeeds();
        }
        //latest update 17/3/2025
        private void LoadCameraFeeds()
        {
            _videoSources = new FilterInfoCollection(FilterCategory.VideoInputDevice);

            if (_videoSources.Count == 0)
            {
                MessageBox.Show("No cameras found.");
                return;
            }

            _videoDevices.Clear();
            _videoFeeds.Clear();
            CameraGrid.Children.Clear();
            CameraGrid.RowDefinitions.Clear();
            CameraGrid.ColumnDefinitions.Clear();

            int numCameras = _videoSources.Count;
            int cols = numCameras; // Horizontal Layout
            int rows = 1; // One row for all cameras

            for (int i = 0; i < rows; i++)
                CameraGrid.RowDefinitions.Add(new RowDefinition());

            for (int i = 0; i < cols; i++)
                CameraGrid.ColumnDefinitions.Add(new ColumnDefinition());

            for (int i = 0; i < numCameras; i++)
            {
                // ✅ Unique Camera ID (MonikerString)
                string cameraID = _videoSources[i].MonikerString;

                // UI Elements
                var container = new StackPanel { Orientation = Orientation.Vertical, Margin = new Thickness(5) };

                var imageControl = new System.Windows.Controls.Image
                {
                    Stretch = System.Windows.Media.Stretch.UniformToFill,
                    VerticalAlignment = VerticalAlignment.Stretch,
                    HorizontalAlignment = HorizontalAlignment.Stretch
                };

                var recordButton = new Button { Content = "Start Recording", Width = 80, Height = 30, Margin = new Thickness(5) };
                var recordingIndicator = new TextBlock
                {
                    Text = "",
                    Foreground = Brushes.Red,
                    FontWeight = FontWeights.Bold,
                    Visibility = Visibility.Hidden,
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                // ✅ Store References Using MonikerString (Unique ID)
                _recordIndicators[cameraID] = recordingIndicator;
                _isRecording[cameraID] = false;
                _recordButtons[cameraID] = recordButton;

                // ✅ Attach Click Event
                recordButton.Click += RecordButton_Click;

                // Add UI to Container
                container.Children.Add(imageControl);
                container.Children.Add(recordingIndicator);
                container.Children.Add(recordButton);

                var border = new Border
                {
                    BorderThickness = new Thickness(1),
                    BorderBrush = Brushes.Gray,
                    Child = container
                };

                // Set Grid Position
                int row = i / cols;
                int col = i % cols;
                Grid.SetRow(border, row);
                Grid.SetColumn(border, col);

                CameraGrid.Children.Add(border);
                _videoFeeds.Add(imageControl);

                // ✅ Initialize Camera Device
                var cameraDevice = new VideoCaptureDevice(cameraID);
                _videoDevices.Add(cameraDevice);

                // Handle New Frame Event
                cameraDevice.NewFrame += (sender, eventArgs) =>
                {
                    Bitmap bitmap = (Bitmap)eventArgs.Frame.Clone();
                    BitmapImage bitmapImage = Utilities.ConvertBitmapToBitmapImage(bitmap);
                    bitmapImage.Freeze();

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        imageControl.Source = bitmapImage;
                    });

                    // ✅ Record Frame if Recording is Enabled
                    if (_isRecording.ContainsKey(cameraID) && _isRecording[cameraID])
                    {
                        _videoWriters[cameraID]?.WriteVideoFrame(bitmap);
                    }
                };

                cameraDevice.Start();
            }
        }

        private void RecordButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button)
            {
                string cameraName = _recordButtons.FirstOrDefault(x => x.Value == button).Key; // Find corresponding camera
                if (cameraName != null)
                {
                    ToggleRecording(cameraName, button, _recordIndicators[cameraName]); // Pass button correctly
                }
            }
        }

        private void ToggleRecording(string cameraName, Button button, TextBlock indicator)
        {
            if (_isRecording[cameraName])
            {
                StopRecording(cameraName);
                button.Content = "Start";
                button.Background = System.Windows.Media.Brushes.LightGray;
                indicator.Visibility = Visibility.Hidden;
            }
            else
            {
                StartRecording(cameraName);
                button.Content = "Stop";
                button.Background = System.Windows.Media.Brushes.Red;
                indicator.Text = "🔴 Recording...";
                indicator.Visibility = Visibility.Visible;
            }
            _isRecording[cameraName] = !_isRecording[cameraName];
        }

        private void StartRecording(string cameraID)
        {
            string simpleCameraName = $"Camera_{_videoDevices.FindIndex(device => device.Source == cameraID)}";

            string outputFile = System.IO.Path.Combine(
                Utilities.GetRecordingFolder(),
                $"{simpleCameraName}_{DateTime.Now:yyyyMMdd_HHmmss}.avi"
            );

            var writer = new VideoFileWriter();
            writer.Open(outputFile, 640, 480, 30, VideoCodec.MPEG4); // Open writer with correct settings

            _videoWriters[cameraID] = writer; // Store writer using MonikerString
        }

        private void StopRecording(string cameraName)
        {
            if (_videoWriters.ContainsKey(cameraName))
            {
                _videoWriters[cameraName].Close();
                _videoWriters[cameraName].Dispose();
                _videoWriters.Remove(cameraName);
            }
        }

        private int GetCameraFPS(string cameraName)
        {
            foreach (FilterInfo device in _videoSources)
            {
                if (device.Name == cameraName)
                {
                    var captureDevice = new VideoCaptureDevice(device.MonikerString);
                    if (captureDevice.VideoCapabilities.Length > 0)
                    {
                        return captureDevice.VideoCapabilities[0].AverageFrameRate; // Get first supported FPS
                    }
                }
            }
            return 30; // Default fallback FPS
        }


        // Start/Stop recording for all cameras
        private void ToggleGlobalRecording(object sender, RoutedEventArgs e)
        {
            foreach (var camera in _videoSources)
            {
                if (camera is FilterInfo filterInfo) // ✅ Ensure correct type
                {
                    string cameraName = filterInfo.Name;

                    if (!_isRecording.ContainsKey(cameraName))
                        _isRecording[cameraName] = false;

                    if (_isRecording[cameraName])
                        StopRecording(cameraName);
                    else
                        StartRecording(cameraName);

                    _isRecording[cameraName] = !_isRecording[cameraName];
                }
            }
        }

        // Handles "Load Recordings" button
        private void LoadRecordings_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "Video Files|*.mp4;*.avi;*.mov;*.mkv",
                Title = "Select a Recording"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                var viewer = new RecordingViewer(openFileDialog.FileName);
                CameraGrid.Children.Add(viewer);
            }
        }

        // Handles selection change in the grid layout combo box
        private void GridLayoutComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            LoadCameraFeeds(); // Reloads feeds based on the new layout
        }

    }
}
