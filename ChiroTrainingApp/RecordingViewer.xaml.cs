﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace ChiroTrainingApp
{
    /// <summary>
    /// Interaction logic for RecordingViewer.xaml
    /// </summary>
    public partial class RecordingViewer : UserControl
    {
        public RecordingViewer(string videoPath)
        {
            InitializeComponent();
            LoadVideo(videoPath);
        }

        private void LoadVideo(string videoPath)
        {
            var mediaElement = new MediaElement
            {
                Source = new Uri(videoPath),
                Width = 320,
                Height = 180,
                LoadedBehavior = MediaState.Manual,
                Margin = new Thickness(5)
            };

            mediaElement.Play();
            VideoContainer.Children.Add(mediaElement);
        }
    }
}
