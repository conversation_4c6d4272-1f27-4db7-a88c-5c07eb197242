C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp.csproj.AssemblyReference.cache
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\RecordingViewer.baml
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\MainWindow.g.cs
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\RecordingViewer.g.cs
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\App.g.cs
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp_Content.g.cs
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp_MarkupCompile.cache
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp_MarkupCompile.lref
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\avcodec-57.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\avdevice-57.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\avfilter-6.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\avformat-57.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\avutil-55.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\postproc-54.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\swresample-2.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\swscale-4.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\Accord.dll.config
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\ChiroTrainingApp.exe.config
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\ChiroTrainingApp.exe
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\ChiroTrainingApp.pdb
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\Accord.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\Accord.Video.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\Accord.Video.FFMPEG.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\AForge.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\AForge.Video.DirectShow.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\AForge.Video.dll
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\Accord.xml
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\Accord.Video.xml
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\Accord.Video.FFMPEG.xml
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\AForge.xml
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\AForge.Video.xml
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\AForge.Video.DirectShow.xml
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\MainWindow.baml
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp.g.resources
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp.Properties.Resources.resources
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp.csproj.GenerateResource.cache
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTra.B75C681B.Up2Date
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp.exe
C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\ChiroTrainingApp.pdb
