   .winmd.dll.exe     ^C:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.3.8.0\build\Accord.dll.configQC:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\packages.config^C:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\Properties\Settings.settingsLC:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\App.confighC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\build\avcodec-57.dlliC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\build\avdevice-57.dllhC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\build\avfilter-6.dlliC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\build\avformat-57.dllgC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\build\avutil-55.dlliC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\build\postproc-54.dlljC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\build\swresample-2.dllgC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\build\swscale-4.dll\C:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.3.8.0\lib\net462\Accord.dllhC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.3.8.0\lib\net462\Accord.Video.dllvC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\Accord.Video.FFMPEG.3.8.0\lib\net462\Accord.Video.FFMPEG.dllUC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\AForge.2.2.5\lib\AForge.dllwC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\AForge.Video.DirectShow.2.2.5\lib\AForge.Video.DirectShow.dllaC:\Users\<USER>\Source\Repos\ChiroTrainingApp\packages\AForge.Video.2.2.5\lib\AForge.Video.dllgC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dllgC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationCore.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationFramework.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dlltC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dlleC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dllfC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Net.Http.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xaml.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dllfC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\WindowsBase.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
{RawFileName}LC:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\bin\Debug\     B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}uC:\Users\<USER>\Source\Repos\ChiroTrainingApp\ChiroTrainingApp\obj\Debug\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\.NETFramework,Version=v4.8.NET Framework 4.8v4.8msil
v4.0.30319         