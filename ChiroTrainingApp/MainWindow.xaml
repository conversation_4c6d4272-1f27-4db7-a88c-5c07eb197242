﻿<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="ChiroTrainingApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ChiroTrainingApp"
        mc:Ignorable="d"
        Title="MainWindow" Height="600" Width="900">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <!-- Global Buttons -->
            <RowDefinition Height="Auto"/>
            <!-- Buttons -->
            <RowDefinition Height="*"/>
            <!-- Camera Grid -->
        </Grid.RowDefinitions>

        <!-- Global Start Button -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Background="#EEE" Margin="5" HorizontalAlignment="Center">
            <Button x:Name="GlobalStartButton" Content="Start All" Click="ToggleGlobalRecording" Width="150" Height="40" Margin="5"/>
        </StackPanel>

        <!-- Controls and Layout Selection -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Background="#EEE" Margin="5" HorizontalAlignment="Center">
            <Button x:Name="LoadRecordingsButton" Content="Load Recordings" Width="150" Height="30" Margin="5" Click="LoadRecordings_Click"/>
            <ComboBox x:Name="GridLayoutComboBox" Width="120" Height="30" Margin="5"
                      SelectionChanged="GridLayoutComboBox_SelectionChanged">
                <ComboBoxItem Content="2x2"/>
                <ComboBoxItem Content="1x4"/>
                <ComboBoxItem Content="4x1"/>
            </ComboBox>
        </StackPanel>

        <!-- Camera Grid -->
        <Grid x:Name="CameraGrid" Grid.Row="2" Background="#CCC" Margin="5">
            <UniformGrid x:Name="CameraContainer" Rows="2" Columns="2" HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
        </Grid>
    </Grid>
</Window>